2025-06-02 14:51:33 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:51:33 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:51:44 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:51:44 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:52:09 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:52:09 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:52:09 | INFO     | vector_store:__init__:49 - Created new collection: legal_documents
2025-06-02 14:52:09 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:52:19 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:52:19 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:52:19 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:52:19 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:50 - Processing documents from: kashewfinaltransactiondocuments
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:61 - Found 10 documents to process
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf: 11 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf: 186 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Bill of Sale (Execution Version).pdf: 7 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Arjomandi) (Execution Version).pdf: 16 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf: 206 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Partial SAFE Repayment and Release Agreement (Execution Version).pdf: 15 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Plan of Dissolution (CA) (Execution Version).pdf: 12 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf: 14 chunks
2025-06-02 14:52:19 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf: 16 chunks
2025-06-02 14:52:20 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Shareholders Consent (Asset Sale and Dissolution) (Execution Version).pdf: 212 chunks
2025-06-02 14:52:20 | INFO     | document_processor:process_documents_folder:73 - Total chunks processed: 695
2025-06-02 14:52:20 | INFO     | vector_store:add_documents:92 - Adding 695 documents to vector store
2025-06-02 14:52:20 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 14:52:31 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 14:52:31 | ERROR    | vector_store:add_documents:125 - Error adding documents to vector store: Expected metadata value to be a str, int, float, bool, or None, got [] which is a list in add.
2025-06-02 14:53:03 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:53:03 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:53:03 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:53:03 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:53:06 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:53:06 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:53:06 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:53:06 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:53:06 | INFO     | vector_store:delete_collection:393 - Collection deleted successfully
2025-06-02 14:53:06 | INFO     | vector_store:reset_collection:405 - Collection reset successfully
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:50 - Processing documents from: kashewfinaltransactiondocuments
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:61 - Found 10 documents to process
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf: 11 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf: 186 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Bill of Sale (Execution Version).pdf: 7 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Arjomandi) (Execution Version).pdf: 16 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf: 206 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Partial SAFE Repayment and Release Agreement (Execution Version).pdf: 15 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Plan of Dissolution (CA) (Execution Version).pdf: 12 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf: 14 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf: 16 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Shareholders Consent (Asset Sale and Dissolution) (Execution Version).pdf: 212 chunks
2025-06-02 14:53:06 | INFO     | document_processor:process_documents_folder:73 - Total chunks processed: 695
2025-06-02 14:53:06 | INFO     | vector_store:add_documents:92 - Adding 695 documents to vector store
2025-06-02 14:53:06 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 14:53:16 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 14:53:16 | ERROR    | vector_store:add_documents:125 - Error adding documents to vector store: Expected metadata value to be a str, int, float, bool, or None, got [] which is a list in add.
2025-06-02 14:53:53 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:53:53 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:53:53 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:53:53 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:53:55 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:53:55 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:53:55 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:53:55 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:53:55 | INFO     | vector_store:delete_collection:393 - Collection deleted successfully
2025-06-02 14:53:55 | INFO     | vector_store:reset_collection:405 - Collection reset successfully
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:50 - Processing documents from: kashewfinaltransactiondocuments
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:61 - Found 10 documents to process
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf: 11 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf: 186 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Bill of Sale (Execution Version).pdf: 7 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Arjomandi) (Execution Version).pdf: 16 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf: 206 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Partial SAFE Repayment and Release Agreement (Execution Version).pdf: 15 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Plan of Dissolution (CA) (Execution Version).pdf: 12 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf: 14 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf: 16 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Shareholders Consent (Asset Sale and Dissolution) (Execution Version).pdf: 212 chunks
2025-06-02 14:53:55 | INFO     | document_processor:process_documents_folder:73 - Total chunks processed: 695
2025-06-02 14:53:55 | INFO     | vector_store:add_documents:92 - Adding 695 documents to vector store
2025-06-02 14:53:55 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 14:54:06 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 14:54:06 | ERROR    | vector_store:add_documents:125 - Error adding documents to vector store: Expected metadata value to be a str, int, float, bool, or None, got [] which is a list in add.
2025-06-02 14:55:18 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:55:18 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:55:18 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:55:18 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:55:21 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:55:21 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:55:21 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:55:21 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:55:21 | INFO     | vector_store:delete_collection:393 - Collection deleted successfully
2025-06-02 14:55:21 | INFO     | vector_store:reset_collection:405 - Collection reset successfully
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:50 - Processing documents from: kashewfinaltransactiondocuments
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:61 - Found 10 documents to process
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf: 11 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf: 186 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Bill of Sale (Execution Version).pdf: 7 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Arjomandi) (Execution Version).pdf: 16 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf: 206 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Partial SAFE Repayment and Release Agreement (Execution Version).pdf: 15 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Plan of Dissolution (CA) (Execution Version).pdf: 12 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf: 14 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf: 16 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Shareholders Consent (Asset Sale and Dissolution) (Execution Version).pdf: 212 chunks
2025-06-02 14:55:21 | INFO     | document_processor:process_documents_folder:73 - Total chunks processed: 695
2025-06-02 14:55:21 | INFO     | vector_store:add_documents:92 - Adding 695 documents to vector store
2025-06-02 14:55:21 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 14:55:31 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 14:55:31 | ERROR    | vector_store:add_documents:125 - Error adding documents to vector store: Expected metadata value to be a str, int, float, bool, or None, got [] which is a list in add.
2025-06-02 14:56:10 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:56:10 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:56:10 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:56:10 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:56:12 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:56:12 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:56:12 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:56:12 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:56:12 | INFO     | vector_store:delete_collection:393 - Collection deleted successfully
2025-06-02 14:56:12 | INFO     | vector_store:reset_collection:405 - Collection reset successfully
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:50 - Processing documents from: kashewfinaltransactiondocuments
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:61 - Found 10 documents to process
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf: 11 chunks
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:68 - Processed Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf: 186 chunks
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Bill of Sale (Execution Version).pdf: 7 chunks
2025-06-02 14:56:12 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Arjomandi) (Execution Version).pdf: 16 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf: 206 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Partial SAFE Repayment and Release Agreement (Execution Version).pdf: 15 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Plan of Dissolution (CA) (Execution Version).pdf: 12 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf: 14 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf: 16 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:68 - Processed Kashew - Shareholders Consent (Asset Sale and Dissolution) (Execution Version).pdf: 212 chunks
2025-06-02 14:56:13 | INFO     | document_processor:process_documents_folder:73 - Total chunks processed: 695
2025-06-02 14:56:13 | INFO     | vector_store:add_documents:92 - Adding 695 documents to vector store
2025-06-02 14:56:13 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 14:56:24 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 14:56:24 | INFO     | vector_store:add_documents:121 - Successfully added 695 documents
2025-06-02 14:56:42 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:56:42 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:56:42 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:56:42 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:56:44 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:56:44 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:56:44 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:56:44 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 14:56:57 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 14:56:57 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 14:56:57 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 14:56:57 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 14:57:01 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 14:57:01 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 14:57:01 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 14:57:01 | INFO     | llm_handler:_load_model:48 - Loading LLM model: Equall/Saul-7B-Instruct-v1
2025-06-02 15:07:31 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:07:31 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:07:31 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:07:34 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:07:34 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:07:34 | INFO     | vector_store:add_documents:92 - Adding 2 documents to vector store
2025-06-02 15:07:34 | INFO     | vector_store:add_documents:109 - Generating embeddings...
2025-06-02 15:07:34 | INFO     | vector_store:add_documents:113 - Adding to vector database...
2025-06-02 15:07:34 | INFO     | vector_store:add_documents:121 - Successfully added 2 documents
2025-06-02 15:07:34 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:07:34 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:07:34 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:07:37 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:07:37 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:10:37 | INFO     | llm_handler:_load_model:101 - Model loaded successfully on device: mps
2025-06-02 15:10:37 | INFO     | llm_handler:__init__:30 - LegalLLMHandler initialized
2025-06-02 15:10:37 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 15:10:37 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What are the key terms in the assignment agreements?...
2025-06-02 15:10:38 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 15:48:46 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 15:48:46 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:48:46 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:48:46 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:48:49 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:48:49 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:48:49 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 15:48:49 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 15:51:56 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 15:51:56 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:51:56 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:51:56 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:51:58 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:51:58 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:51:58 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 15:51:58 | INFO     | llm_handler:_load_model:48 - Loading LLM model: Equall/Saul-7B-Instruct-v1
2025-06-02 15:55:08 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 15:55:08 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:55:09 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:55:09 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:55:13 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:55:13 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:55:13 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 15:55:13 | INFO     | llm_handler:_load_model:48 - Loading LLM model: microsoft/DialoGPT-medium
2025-06-02 15:55:36 | INFO     | llm_handler:_load_model:101 - Model loaded successfully on device: mps
2025-06-02 15:55:36 | INFO     | llm_handler:__init__:30 - LegalLLMHandler initialized
2025-06-02 15:55:36 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 15:55:36 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is Kashew?...
2025-06-02 15:55:36 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 15:56:56 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 15:56:56 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 15:56:56 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 15:56:56 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 15:56:59 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 15:56:59 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 15:56:59 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 15:56:59 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 17:33:26 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 17:33:26 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 17:33:26 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 17:33:26 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 17:33:29 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 17:33:29 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 17:33:29 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 17:33:29 | INFO     | llm_handler:_load_model:61 - Loading LLM model: Equall/Saul-7B-Instruct-v1
2025-06-02 17:39:51 | INFO     | llm_handler:_load_model:114 - Model loaded successfully on device: mps
2025-06-02 17:39:51 | INFO     | llm_handler:__init__:43 - LegalLLMHandler initialized
2025-06-02 17:39:51 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 17:39:51 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What are the key terms in the assignment agreements?...
2025-06-02 17:39:51 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 17:41:26 | ERROR    | llm_handler:generate_response:181 - Error generating response: MPS backend out of memory (MPS allocated: 14.11 GB, other allocations: 11.67 GB, max allowed: 18.13 GB). Tried to allocate 1.50 KB on private pool. Use PYTORCH_MPS_HIGH_WATERMARK_RATIO=0.0 to disable upper limit for memory allocations (may cause system failure).
2025-06-02 17:52:01 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 17:52:01 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 17:52:01 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 17:52:01 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 17:52:03 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 17:52:03 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 17:52:03 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 17:52:03 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 17:53:14 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 17:53:14 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 17:53:14 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 17:53:14 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 17:53:16 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 17:53:16 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 17:53:16 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 17:53:16 | INFO     | llm_handler:_load_model:61 - Loading LLM model: Equall/Saul-7B-Instruct-v1
2025-06-02 17:53:49 | ERROR    | llm_handler:_load_model:123 - Error loading model: You can't move a model that has some modules offloaded to cpu or disk.
2025-06-02 17:54:30 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 17:54:30 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 17:54:30 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 17:54:30 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 17:54:32 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 17:54:32 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 17:54:32 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 17:54:32 | INFO     | llm_handler:_load_model:61 - Loading LLM model: Equall/Saul-7B-Instruct-v1
2025-06-02 17:55:16 | INFO     | llm_handler:_load_model:123 - Model loaded successfully on device: mps
2025-06-02 17:55:16 | INFO     | llm_handler:__init__:43 - LegalLLMHandler initialized
2025-06-02 17:55:16 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 17:55:16 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is the purpose of the assignment agreements?...
2025-06-02 17:55:16 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 18:00:16 | ERROR    | llm_handler:generate_response:178 - LLM generation timed out after 5 minutes
2025-06-02 18:01:10 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 18:01:10 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 18:01:10 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 18:01:10 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 18:01:12 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 18:01:12 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 18:01:12 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 18:01:12 | INFO     | llm_handler:_load_model:61 - Loading LLM model: microsoft/DialoGPT-medium
2025-06-02 18:01:15 | INFO     | llm_handler:_load_model:123 - Model loaded successfully on device: mps
2025-06-02 18:01:15 | INFO     | llm_handler:__init__:43 - LegalLLMHandler initialized
2025-06-02 18:01:15 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 18:01:15 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is Kashew?...
2025-06-02 18:01:15 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 18:05:32 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 18:05:32 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 18:05:32 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 18:05:32 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 18:05:34 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 18:05:34 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 18:05:34 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 18:05:34 | INFO     | llm_handler:_load_model:70 - Loading LLM model: MaziyarPanahi/Saul-Instruct-v1-GGUF
2025-06-02 18:05:34 | INFO     | llm_handler:_load_gguf_model:92 - Loading GGUF model: MaziyarPanahi/Saul-Instruct-v1-GGUF/Saul-Instruct-v1-GGUF.Q4_K_M.gguf
2025-06-02 18:05:35 | ERROR    | llm_handler:_load_gguf_model:105 - Failed to download model file: 404 Client Error. (Request ID: Root=1-683dcbcf-4e1b890f265975b86a2b0ed3;68c620c6-1b21-4302-a34b-04bea4e43346)

Entry Not Found for url: https://huggingface.co/MaziyarPanahi/Saul-Instruct-v1-GGUF/resolve/main/Saul-Instruct-v1-GGUF.Q4_K_M.gguf.
2025-06-02 18:05:35 | ERROR    | llm_handler:_load_model:81 - Error loading model: 404 Client Error. (Request ID: Root=1-683dcbcf-4e1b890f265975b86a2b0ed3;68c620c6-1b21-4302-a34b-04bea4e43346)

Entry Not Found for url: https://huggingface.co/MaziyarPanahi/Saul-Instruct-v1-GGUF/resolve/main/Saul-Instruct-v1-GGUF.Q4_K_M.gguf.
2025-06-02 18:06:12 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 18:06:12 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 18:06:12 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 18:06:12 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 18:06:14 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 18:06:14 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 18:06:14 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 18:06:14 | INFO     | llm_handler:_load_model:70 - Loading LLM model: MaziyarPanahi/Saul-Instruct-v1-GGUF
2025-06-02 18:06:14 | INFO     | llm_handler:_load_gguf_model:92 - Loading GGUF model: MaziyarPanahi/Saul-Instruct-v1-GGUF/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:16:41 | INFO     | llm_handler:_load_gguf_model:103 - Model file downloaded to: ./models/models--MaziyarPanahi--Saul-Instruct-v1-GGUF/snapshots/69ef4c546503ea2ce1d0d2020f3dd525964ce88c/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:17:02 | INFO     | llm_handler:_load_gguf_model:125 - GGUF model loaded successfully with 35 GPU layers
2025-06-02 18:17:02 | INFO     | llm_handler:__init__:52 - LegalLLMHandler initialized
2025-06-02 18:17:02 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 18:17:02 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is the purpose of the assignment agreements?...
2025-06-02 18:17:02 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 18:17:02 | ERROR    | llm_handler:_generate_with_gguf:236 - Error with GGUF generation: Requested tokens (1463) exceed context window of 512
2025-06-02 18:17:02 | ERROR    | llm_handler:generate_response:215 - Error generating response: Requested tokens (1463) exceed context window of 512
2025-06-02 18:17:31 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 18:17:31 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 18:17:32 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 18:17:32 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 18:17:34 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 18:17:34 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 18:17:34 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 18:17:34 | INFO     | llm_handler:_load_model:70 - Loading LLM model: MaziyarPanahi/Saul-Instruct-v1-GGUF
2025-06-02 18:17:34 | INFO     | llm_handler:_load_gguf_model:92 - Loading GGUF model: MaziyarPanahi/Saul-Instruct-v1-GGUF/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:17:34 | INFO     | llm_handler:_load_gguf_model:103 - Model file downloaded to: ./models/models--MaziyarPanahi--Saul-Instruct-v1-GGUF/snapshots/69ef4c546503ea2ce1d0d2020f3dd525964ce88c/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:17:34 | INFO     | llm_handler:_load_gguf_model:125 - GGUF model loaded successfully with 35 GPU layers
2025-06-02 18:17:34 | INFO     | llm_handler:__init__:52 - LegalLLMHandler initialized
2025-06-02 18:17:34 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 18:17:34 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is the purpose of the assignment agreements?...
2025-06-02 18:17:35 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
2025-06-02 18:18:21 | INFO     | __main__:__init__:37 - LegalRAGSystem created
2025-06-02 18:18:21 | INFO     | document_processor:__init__:31 - DocumentProcessor initialized
2025-06-02 18:18:21 | INFO     | vector_store:__init__:43 - Loaded existing collection: legal_documents
2025-06-02 18:18:21 | INFO     | vector_store:_load_embedding_model:61 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-06-02 18:18:25 | INFO     | vector_store:_load_embedding_model:72 - Embedding model loaded on device: cpu
2025-06-02 18:18:25 | INFO     | vector_store:__init__:55 - VectorStore initialized
2025-06-02 18:18:25 | INFO     | retrieval_engine:__init__:23 - LegalRetrievalEngine initialized
2025-06-02 18:18:25 | INFO     | llm_handler:_load_model:70 - Loading LLM model: MaziyarPanahi/Saul-Instruct-v1-GGUF
2025-06-02 18:18:25 | INFO     | llm_handler:_load_gguf_model:92 - Loading GGUF model: MaziyarPanahi/Saul-Instruct-v1-GGUF/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:18:25 | INFO     | llm_handler:_load_gguf_model:103 - Model file downloaded to: ./models/models--MaziyarPanahi--Saul-Instruct-v1-GGUF/snapshots/69ef4c546503ea2ce1d0d2020f3dd525964ce88c/Saul-Instruct-v1.Q4_K_M.gguf
2025-06-02 18:18:26 | INFO     | llm_handler:_load_gguf_model:125 - GGUF model loaded successfully with 35 GPU layers
2025-06-02 18:18:26 | INFO     | llm_handler:__init__:52 - LegalLLMHandler initialized
2025-06-02 18:18:26 | INFO     | __main__:initialize:74 - LegalRAGSystem initialized successfully
2025-06-02 18:18:26 | INFO     | retrieval_engine:retrieve:71 - Retrieving documents for query: What is the purpose of the assignment agreements?...
2025-06-02 18:18:26 | INFO     | retrieval_engine:retrieve:105 - Retrieved 10 relevant documents
