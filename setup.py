"""
Setup script for the Legal RAG System.
Handles installation, environment setup, and initial configuration.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Dict, Any
import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn


class LegalRAGSetup:
    """Setup and installation manager for the Legal RAG System."""
    
    def __init__(self):
        self.console = Console()
        self.python_version = sys.version_info
        self.platform = platform.system()
        
    def check_requirements(self) -> bool:
        """Check system requirements."""
        self.console.print(Panel(
            "[bold blue]Checking System Requirements[/bold blue]",
            title="Setup"
        ))
        
        # Check Python version
        if self.python_version < (3, 8):
            self.console.print("[red]Error: Python 3.8 or higher is required[/red]")
            return False
        
        self.console.print(f"[green]✓[/green] Python {self.python_version.major}.{self.python_version.minor}")
        
        # Check available memory (rough estimate)
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            self.console.print(f"[green]✓[/green] Available RAM: {memory_gb:.1f} GB")
            
            if memory_gb < 8:
                self.console.print("[yellow]Warning: Less than 8GB RAM detected. Consider using quantization.[/yellow]")
        except ImportError:
            self.console.print("[yellow]Could not check memory requirements[/yellow]")
        
        # Check GPU availability
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                self.console.print(f"[green]✓[/green] CUDA GPU available: {gpu_name} ({gpu_count} device(s))")
            else:
                self.console.print("[yellow]No CUDA GPU detected - will use CPU[/yellow]")
        except ImportError:
            self.console.print("[yellow]PyTorch not installed yet[/yellow]")
        
        return True
    
    def install_dependencies(self, use_gpu: bool = True):
        """Install Python dependencies."""
        self.console.print(Panel(
            "[bold blue]Installing Dependencies[/bold blue]",
            title="Setup"
        ))
        
        # Determine PyTorch installation command based on platform and GPU
        if use_gpu and self.platform in ["Linux", "Windows"]:
            torch_cmd = ["pip", "install", "torch", "torchvision", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu118"]
        else:
            torch_cmd = ["pip", "install", "torch", "torchvision", "torchaudio"]
        
        commands = [
            # Upgrade pip
            ["pip", "install", "--upgrade", "pip"],
            
            # Install PyTorch first
            torch_cmd,
            
            # Install other requirements
            ["pip", "install", "-r", "requirements.txt"],
            
            # Download spaCy model
            ["python", "-m", "spacy", "download", "en_core_web_sm"],
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            for i, cmd in enumerate(commands):
                task = progress.add_task(f"Running: {' '.join(cmd[:3])}...", total=None)
                
                try:
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    progress.update(task, completed=True)
                    self.console.print(f"[green]✓[/green] {' '.join(cmd[:3])}")
                    
                except subprocess.CalledProcessError as e:
                    progress.update(task, completed=True)
                    self.console.print(f"[red]✗[/red] {' '.join(cmd[:3])}: {e}")
                    if "spacy download" not in ' '.join(cmd):
                        return False
        
        return True
    
    def setup_directories(self):
        """Create necessary directories."""
        directories = [
            "models",
            "vector_db", 
            "cache",
            "cache/processed_docs",
            "logs",
            "examples"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.console.print(f"[green]✓[/green] Created directory: {directory}")
    
    def create_env_file(self):
        """Create a sample .env file."""
        env_content = """# Legal RAG System Configuration
# Copy this file to .env and modify as needed

# Model Configuration
LLM_MODEL_NAME=Equall/Saul-7B-Instruct-v1
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MODEL_CACHE_DIR=./models

# Document Processing
DOCUMENTS_FOLDER=./kashewfinaltransactiondocuments
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Vector Database
VECTOR_DB_PATH=./vector_db
COLLECTION_NAME=legal_documents

# System Settings
LOG_LEVEL=INFO
MAX_WORKERS=4
ENABLE_GPU=true

# API Settings (for future web interface)
API_HOST=localhost
API_PORT=8000
"""
        
        env_file = Path(".env.example")
        with open(env_file, "w") as f:
            f.write(env_content)
        
        self.console.print(f"[green]✓[/green] Created {env_file}")
        self.console.print("[yellow]Copy .env.example to .env and modify as needed[/yellow]")
    
    def download_models(self):
        """Download and cache models."""
        self.console.print(Panel(
            "[bold blue]Downloading Models[/bold blue]",
            title="Setup"
        ))
        
        try:
            # Test model loading
            from sentence_transformers import SentenceTransformer
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                
                # Download embedding model
                task1 = progress.add_task("Downloading embedding model...", total=None)
                model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2", cache_folder="./models")
                progress.update(task1, completed=True)
                self.console.print("[green]✓[/green] Embedding model downloaded")
                
                # Note about LLM model
                self.console.print("[yellow]Note: The Saul-7B model will be downloaded on first use[/yellow]")
                self.console.print("[yellow]This may take 10-15 minutes depending on your internet connection[/yellow]")
        
        except Exception as e:
            self.console.print(f"[red]Error downloading models: {e}[/red]")
            return False
        
        return True
    
    def run_initial_test(self):
        """Run initial system test."""
        self.console.print(Panel(
            "[bold blue]Running Initial Test[/bold blue]",
            title="Setup"
        ))
        
        try:
            # Test document processing
            from document_processor import DocumentProcessor
            processor = DocumentProcessor()
            
            # Test vector store
            from vector_store import VectorStore
            vector_store = VectorStore()
            
            self.console.print("[green]✓[/green] All components loaded successfully")
            
            # Check if documents exist
            docs_folder = Path("./kashewfinaltransactiondocuments")
            if docs_folder.exists():
                pdf_files = list(docs_folder.glob("*.pdf"))
                self.console.print(f"[green]✓[/green] Found {len(pdf_files)} PDF documents")
            else:
                self.console.print("[yellow]No documents folder found - you can add documents later[/yellow]")
            
            return True
            
        except Exception as e:
            self.console.print(f"[red]Test failed: {e}[/red]")
            return False
    
    def display_next_steps(self):
        """Display next steps for the user."""
        next_steps = """
[bold green]Setup Complete![/bold green]

[bold]Next Steps:[/bold]

1. [cyan]Index your documents:[/cyan]
   python legal_rag_system.py index

2. [cyan]Query the system:[/cyan]
   python legal_rag_system.py query "What are the key terms in the assignment agreement?"

3. [cyan]Analyze a specific document:[/cyan]
   python legal_rag_system.py analyze path/to/document.pdf

4. [cyan]Check system status:[/cyan]
   python legal_rag_system.py status

[bold]Configuration:[/bold]
- Edit .env file to customize settings
- Place PDF documents in the documents folder
- Check logs/ directory for detailed logs

[bold]Hardware Recommendations:[/bold]
- 16GB+ RAM for optimal performance
- NVIDIA GPU with 8GB+ VRAM for faster inference
- SSD storage for better I/O performance
"""
        
        self.console.print(Panel(next_steps, title="Setup Complete"))


@click.command()
@click.option('--gpu/--no-gpu', default=True, help='Install GPU support')
@click.option('--skip-models', is_flag=True, help='Skip model download')
@click.option('--skip-test', is_flag=True, help='Skip initial test')
def setup(gpu, skip_models, skip_test):
    """Set up the Legal RAG System."""
    setup_manager = LegalRAGSetup()
    
    # Check requirements
    if not setup_manager.check_requirements():
        sys.exit(1)
    
    # Install dependencies
    if not setup_manager.install_dependencies(use_gpu=gpu):
        setup_manager.console.print("[red]Failed to install dependencies[/red]")
        sys.exit(1)
    
    # Setup directories
    setup_manager.setup_directories()
    
    # Create env file
    setup_manager.create_env_file()
    
    # Download models
    if not skip_models:
        if not setup_manager.download_models():
            setup_manager.console.print("[yellow]Model download failed, but you can continue[/yellow]")
    
    # Run test
    if not skip_test:
        if not setup_manager.run_initial_test():
            setup_manager.console.print("[yellow]Initial test failed, but setup is complete[/yellow]")
    
    # Display next steps
    setup_manager.display_next_steps()


if __name__ == "__main__":
    setup()
