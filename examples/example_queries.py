"""
Example queries and usage patterns for the Legal RAG System.
"""

from legal_rag_system import LegalRAGSystem
from rich.console import Console
from rich.panel import Panel


def run_example_queries():
    """Run a series of example queries to demonstrate the system."""
    console = Console()
    
    # Initialize the system
    console.print(Panel("[bold blue]Initializing Legal RAG System[/bold blue]", title="Demo"))
    system = LegalRAGSystem()
    system.initialize()
    
    # Example queries
    example_queries = [
        {
            "query": "What are the key terms and conditions in the assignment agreements?",
            "description": "General contract analysis"
        },
        {
            "query": "Explain the indemnification provisions across all documents",
            "description": "Risk and liability analysis"
        },
        {
            "query": "What intellectual property rights are being transferred?",
            "description": "IP rights analysis"
        },
        {
            "query": "Summarize the termination and dissolution procedures",
            "description": "Contract termination analysis"
        },
        {
            "query": "What are the governing law and jurisdiction clauses?",
            "description": "Legal jurisdiction analysis"
        },
        {
            "query": "Describe the consideration and payment terms",
            "description": "Financial terms analysis"
        },
        {
            "query": "What representations and warranties are provided?",
            "description": "Legal assurances analysis"
        }
    ]
    
    # Run each query
    for i, example in enumerate(example_queries, 1):
        console.print(f"\n[bold cyan]Example {i}: {example['description']}[/bold cyan]")
        console.print(f"[dim]Query: {example['query']}[/dim]\n")
        
        try:
            result = system.query(example["query"], top_k=3)
            
            if "error" in result:
                console.print(f"[red]Error: {result['error']}[/red]")
                continue
            
            # Display response
            console.print(Panel(
                result["response"],
                title=f"Response {i}",
                border_style="green"
            ))
            
            # Display source information
            if result.get("sources"):
                console.print(f"[dim]Sources: {len(result['sources'])} documents, "
                            f"Retrieved in {result['retrieval_time']:.2f}s[/dim]")
            
        except Exception as e:
            console.print(f"[red]Error processing query: {e}[/red]")
        
        # Pause between queries
        input("\nPress Enter to continue to next example...")
    
    console.print(Panel("[bold green]Demo completed![/bold green]", title="Demo"))


def demonstrate_document_analysis():
    """Demonstrate document analysis features."""
    console = Console()
    
    console.print(Panel("[bold blue]Document Analysis Demo[/bold blue]", title="Demo"))
    
    system = LegalRAGSystem()
    system.initialize()
    
    # Find a sample document
    import os
    docs_folder = "./kashewfinaltransactiondocuments"
    
    if os.path.exists(docs_folder):
        pdf_files = [f for f in os.listdir(docs_folder) if f.endswith('.pdf')]
        
        if pdf_files:
            sample_doc = os.path.join(docs_folder, pdf_files[0])
            console.print(f"[cyan]Analyzing: {pdf_files[0]}[/cyan]\n")
            
            # Different types of analysis
            analysis_types = [
                ("general", "General Analysis"),
                ("risks", "Risk Assessment"),
                ("summary", "Document Summary"),
                ("compliance", "Compliance Review")
            ]
            
            for analysis_type, description in analysis_types:
                console.print(f"[bold yellow]{description}:[/bold yellow]")
                
                try:
                    result = system.analyze_document(sample_doc, analysis_type)
                    console.print(Panel(result, border_style="blue"))
                    
                except Exception as e:
                    console.print(f"[red]Error: {e}[/red]")
                
                input("\nPress Enter for next analysis...")
        else:
            console.print("[yellow]No PDF files found for analysis demo[/yellow]")
    else:
        console.print("[yellow]Documents folder not found for analysis demo[/yellow]")


def show_system_capabilities():
    """Show system capabilities and statistics."""
    console = Console()
    
    console.print(Panel("[bold blue]System Capabilities[/bold blue]", title="Demo"))
    
    system = LegalRAGSystem()
    system.initialize(load_llm=False)  # Skip LLM for faster demo
    
    # Show system status
    status = system.get_system_status()
    
    console.print("[bold]System Components:[/bold]")
    for component, loaded in status["components"].items():
        status_icon = "✓" if loaded else "✗"
        console.print(f"  {status_icon} {component.replace('_', ' ').title()}")
    
    # Show vector store stats
    if "vector_store_stats" in status:
        stats = status["vector_store_stats"]
        console.print(f"\n[bold]Vector Store:[/bold]")
        console.print(f"  Documents indexed: {stats.get('total_documents', 0)}")
        console.print(f"  Collection: {stats.get('collection_name', 'N/A')}")
        console.print(f"  Embedding model: {stats.get('embedding_model', 'N/A')}")
        
        doc_types = stats.get('document_types', {})
        if doc_types:
            console.print(f"\n[bold]Document Types:[/bold]")
            for doc_type, count in doc_types.items():
                console.print(f"  {doc_type}: {count}")
    
    # Show configuration highlights
    config_data = status["configuration"]
    console.print(f"\n[bold]Configuration Highlights:[/bold]")
    console.print(f"  LLM Model: {config_data['model']['llm_model_name']}")
    console.print(f"  Embedding Model: {config_data['model']['embedding_model_name']}")
    console.print(f"  Chunk Size: {config_data['document']['chunk_size']}")
    console.print(f"  Top-K Results: {config_data['retrieval']['top_k']}")


if __name__ == "__main__":
    import sys
    
    console = Console()
    
    if len(sys.argv) > 1:
        demo_type = sys.argv[1]
        
        if demo_type == "queries":
            run_example_queries()
        elif demo_type == "analysis":
            demonstrate_document_analysis()
        elif demo_type == "capabilities":
            show_system_capabilities()
        else:
            console.print(f"[red]Unknown demo type: {demo_type}[/red]")
            console.print("Available demos: queries, analysis, capabilities")
    else:
        console.print("[bold]Legal RAG System Demo[/bold]\n")
        console.print("Available demos:")
        console.print("  python examples/example_queries.py queries      - Run example queries")
        console.print("  python examples/example_queries.py analysis     - Document analysis demo")
        console.print("  python examples/example_queries.py capabilities - Show system capabilities")
