"""
Main Legal RAG System application.
Orchestrates document processing, vector storage, retrieval, and LLM generation.
"""

import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from config import config
from document_processor import DocumentProcessor
from vector_store import VectorStore
from retrieval_engine import LegalRetrievalEngine
from llm_handler import LegalLLMHandler
from utils import format_legal_response, validate_query


class LegalRAGSystem:
    """Main Legal RAG System class."""
    
    def __init__(self):
        self.console = Console()
        self.document_processor = None
        self.vector_store = None
        self.retrieval_engine = None
        self.llm_handler = None
        self.is_initialized = False
        
        logger.info("LegalRAGSystem created")
    
    def initialize(self, load_llm: bool = True):
        """
        Initialize all system components.
        
        Args:
            load_llm: Whether to load the LLM (can be skipped for indexing only)
        """
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            # Initialize document processor
            task1 = progress.add_task("Initializing document processor...", total=None)
            self.document_processor = DocumentProcessor()
            progress.update(task1, completed=True)
            
            # Initialize vector store
            task2 = progress.add_task("Initializing vector store...", total=None)
            self.vector_store = VectorStore()
            progress.update(task2, completed=True)
            
            # Initialize retrieval engine
            task3 = progress.add_task("Initializing retrieval engine...", total=None)
            self.retrieval_engine = LegalRetrievalEngine(self.vector_store)
            progress.update(task3, completed=True)
            
            # Initialize LLM handler
            if load_llm:
                task4 = progress.add_task("Loading LLM model (this may take a while)...", total=None)
                self.llm_handler = LegalLLMHandler()
                progress.update(task4, completed=True)
        
        self.is_initialized = True
        logger.info("LegalRAGSystem initialized successfully")
    
    def index_documents(self, documents_folder: str = None, force_reindex: bool = False):
        """
        Index documents from the specified folder.
        
        Args:
            documents_folder: Path to documents folder
            force_reindex: Whether to force reindexing of all documents
        """
        if not self.is_initialized:
            self.initialize(load_llm=False)
        
        if documents_folder:
            config.update_documents_folder(documents_folder)
        
        self.console.print(Panel(
            f"[bold blue]Indexing documents from:[/bold blue] {config.document.documents_folder}",
            title="Document Indexing"
        ))
        
        # Get document statistics
        stats = self.document_processor.get_document_stats()
        self._display_document_stats(stats)
        
        if stats["total_files"] == 0:
            self.console.print("[red]No documents found to index![/red]")
            return
        
        # Check if we need to reset the collection
        if force_reindex:
            self.console.print("[yellow]Force reindexing - resetting vector store...[/yellow]")
            self.vector_store.reset_collection()
        
        # Process documents
        with Progress(console=self.console) as progress:
            task = progress.add_task("Processing documents...", total=stats["total_files"])
            
            try:
                processed_chunks = self.document_processor.process_documents_folder()
                progress.update(task, advance=stats["total_files"])
                
                if not processed_chunks:
                    self.console.print("[red]No document chunks were processed![/red]")
                    return
                
                # Add to vector store
                indexing_task = progress.add_task("Adding to vector store...", total=len(processed_chunks))
                success = self.vector_store.add_documents(processed_chunks)
                progress.update(indexing_task, advance=len(processed_chunks))
                
                if success:
                    self.console.print(f"[green]Successfully indexed {len(processed_chunks)} document chunks![/green]")
                    self._display_indexing_stats()
                else:
                    self.console.print("[red]Failed to add documents to vector store![/red]")
                    
            except Exception as e:
                logger.error(f"Error during indexing: {e}")
                self.console.print(f"[red]Error during indexing: {e}[/red]")
    
    def query(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Query the legal RAG system.
        
        Args:
            query_text: Query string
            top_k: Number of documents to retrieve
        
        Returns:
            Dictionary with query results
        """
        if not self.is_initialized or not self.llm_handler:
            self.initialize(load_llm=True)
        
        # Validate query
        is_valid, error_msg = validate_query(query_text)
        if not is_valid:
            return {"error": error_msg}
        
        start_time = time.time()
        
        try:
            # Retrieve relevant documents
            with Progress(console=self.console) as progress:
                retrieval_task = progress.add_task("Retrieving relevant documents...", total=None)
                
                retrieved_docs = self.retrieval_engine.retrieve(
                    query_text,
                    top_k=top_k,
                    use_hybrid_search=True,
                    include_citations=True
                )
                progress.update(retrieval_task, completed=True)
                
                if not retrieved_docs:
                    return {
                        "query": query_text,
                        "response": "I couldn't find any relevant documents for your query. Please try rephrasing your question or check if documents have been indexed.",
                        "sources": [],
                        "retrieval_time": time.time() - start_time
                    }
                
                # Generate response
                generation_task = progress.add_task("Generating response...", total=None)
                
                response = self.llm_handler.generate_response(
                    query_text,
                    retrieved_docs
                )
                progress.update(generation_task, completed=True)
            
            # Format response with sources
            formatted_response = format_legal_response(response, retrieved_docs)
            
            total_time = time.time() - start_time
            
            return {
                "query": query_text,
                "response": formatted_response,
                "sources": retrieved_docs,
                "retrieval_time": total_time,
                "num_sources": len(retrieved_docs)
            }
            
        except Exception as e:
            logger.error(f"Error during query processing: {e}")
            return {
                "query": query_text,
                "error": f"An error occurred while processing your query: {e}",
                "sources": []
            }
    
    def analyze_document(self, document_path: str, analysis_type: str = "general") -> str:
        """
        Analyze a specific document.
        
        Args:
            document_path: Path to the document
            analysis_type: Type of analysis to perform
        
        Returns:
            Analysis results
        """
        if not self.is_initialized or not self.llm_handler:
            self.initialize(load_llm=True)
        
        try:
            # Process the document
            chunks = self.document_processor.process_document(Path(document_path))
            
            if not chunks:
                return "Could not extract text from the document."
            
            # Combine chunks for analysis
            full_text = "\n\n".join([chunk["text"] for chunk in chunks])
            
            # Analyze with LLM
            analysis = self.llm_handler.analyze_legal_document(full_text, analysis_type)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing document: {e}")
            return f"Error analyzing document: {e}"
    
    def _display_document_stats(self, stats: Dict[str, Any]):
        """Display document statistics in a table."""
        table = Table(title="Document Statistics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Files", str(stats["total_files"]))
        table.add_row("Total Size (MB)", f"{stats['total_size_mb']:.2f}")
        
        for file_type, count in stats["file_types"].items():
            table.add_row(f"Files ({file_type})", str(count))
        
        self.console.print(table)
    
    def _display_indexing_stats(self):
        """Display indexing statistics."""
        vector_stats = self.vector_store.get_collection_stats()
        
        table = Table(title="Indexing Results")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Chunks Indexed", str(vector_stats.get("total_documents", 0)))
        table.add_row("Collection Name", vector_stats.get("collection_name", "N/A"))
        table.add_row("Embedding Model", vector_stats.get("embedding_model", "N/A"))
        
        doc_types = vector_stats.get("document_types", {})
        for doc_type, count in doc_types.items():
            table.add_row(f"Document Type: {doc_type}", str(count))
        
        self.console.print(table)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status information."""
        status = {
            "initialized": self.is_initialized,
            "components": {
                "document_processor": self.document_processor is not None,
                "vector_store": self.vector_store is not None,
                "retrieval_engine": self.retrieval_engine is not None,
                "llm_handler": self.llm_handler is not None
            },
            "configuration": config.to_dict()
        }
        
        if self.vector_store:
            status["vector_store_stats"] = self.vector_store.get_collection_stats()
        
        if self.llm_handler:
            status["llm_info"] = self.llm_handler.get_model_info()
        
        return status
    
    def cleanup(self):
        """Clean up system resources."""
        if self.llm_handler:
            self.llm_handler.cleanup()
        
        logger.info("System cleanup completed")


# CLI Interface
@click.group()
def cli():
    """Legal RAG System - AI-powered legal document analysis."""
    pass


@cli.command()
@click.option('--folder', '-f', help='Documents folder path')
@click.option('--force', is_flag=True, help='Force reindexing of all documents')
def index(folder, force):
    """Index legal documents for search and retrieval."""
    system = LegalRAGSystem()
    system.index_documents(folder, force)


@cli.command()
@click.argument('query')
@click.option('--top-k', '-k', default=5, help='Number of documents to retrieve')
def query(query, top_k):
    """Query the legal document system."""
    system = LegalRAGSystem()
    result = system.query(query, top_k)
    
    console = Console()
    
    if "error" in result:
        console.print(f"[red]Error: {result['error']}[/red]")
        return
    
    console.print(Panel(
        result["response"],
        title=f"Response to: {query}",
        border_style="blue"
    ))
    
    if result.get("sources"):
        console.print(f"\n[dim]Retrieved {len(result['sources'])} sources in {result['retrieval_time']:.2f}s[/dim]")


@cli.command()
@click.argument('document_path')
@click.option('--analysis-type', '-t', default='general', 
              type=click.Choice(['general', 'risks', 'summary', 'compliance']),
              help='Type of analysis to perform')
def analyze(document_path, analysis_type):
    """Analyze a specific legal document."""
    system = LegalRAGSystem()
    result = system.analyze_document(document_path, analysis_type)
    
    console = Console()
    console.print(Panel(
        result,
        title=f"Analysis of {Path(document_path).name}",
        border_style="green"
    ))


@cli.command()
def status():
    """Show system status and statistics."""
    system = LegalRAGSystem()
    system.initialize(load_llm=False)
    
    status = system.get_system_status()
    console = Console()
    
    # Display component status
    table = Table(title="System Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    
    for component, loaded in status["components"].items():
        status_text = "✓ Loaded" if loaded else "✗ Not Loaded"
        table.add_row(component.replace("_", " ").title(), status_text)
    
    console.print(table)
    
    # Display vector store stats if available
    if "vector_store_stats" in status:
        stats = status["vector_store_stats"]
        console.print(f"\n[bold]Vector Store:[/bold] {stats.get('total_documents', 0)} documents indexed")


if __name__ == "__main__":
    cli()
