# Legal RAG System

A comprehensive Retrieval-Augmented Generation (RAG) system specifically designed for legal document analysis using the Equall/Saul-7B-Instruct-v1 model. This system processes PDF legal documents, creates intelligent embeddings, and enables sophisticated querying with legal domain expertise.

## 🚀 Features

### Document Processing
- **Multi-format Support**: PDF, DOCX, and TXT files
- **Legal Structure Preservation**: Maintains document hierarchy, sections, and citations
- **Intelligent Chunking**: Respects legal document boundaries and context
- **OCR Support**: Handles scanned documents (optional)
- **Metadata Extraction**: Captures document type, citations, and structural information

### Vector Database & Search
- **ChromaDB Integration**: Local, persistent vector storage
- **Hybrid Search**: Combines semantic similarity with keyword matching
- **Legal Domain Embeddings**: Optimized for legal terminology and concepts
- **Citation Tracking**: Maintains and searches legal citations and cross-references
- **Advanced Filtering**: Filter by document type, section, and legal concepts

### LLM Integration
- **Saul-7B Model**: Specialized legal language model from Equall
- **Legal Prompt Engineering**: Optimized prompts for legal analysis
- **Citation-Aware Responses**: Includes source references in generated answers
- **Multiple Analysis Types**: General analysis, risk assessment, compliance review

### Performance & Optimization
- **GPU Acceleration**: CUDA support with automatic fallback to CPU
- **Model Quantization**: 4-bit quantization for memory efficiency
- **Batch Processing**: Efficient document ingestion and embedding generation
- **Caching System**: Intelligent caching for processed documents and embeddings

## 📋 Requirements

### System Requirements
- **Python**: 3.8 or higher
- **RAM**: 8GB minimum, 16GB+ recommended
- **Storage**: 20GB+ free space for models and data
- **GPU**: NVIDIA GPU with 8GB+ VRAM (optional but recommended)

### Hardware Recommendations
- **CPU**: Multi-core processor (8+ cores recommended)
- **RAM**: 32GB for large document collections
- **GPU**: RTX 3080/4080 or better for optimal performance
- **Storage**: SSD for better I/O performance

## 🛠️ Installation

### Quick Setup
```bash
# Clone or download the system files
cd legal-rag-system

# Run the automated setup
python setup.py

# Follow the prompts for GPU support and model downloads
```

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Download spaCy model
python -m spacy download en_core_web_sm

# Create necessary directories
mkdir -p models vector_db cache logs examples

# Copy and configure environment
cp .env.example .env
# Edit .env file with your settings
```

## 🚀 Quick Start

### 1. Index Your Documents
```bash
# Index documents from the default folder
python legal_rag_system.py index

# Index from a specific folder
python legal_rag_system.py index --folder /path/to/your/documents

# Force reindexing of all documents
python legal_rag_system.py index --force
```

### 2. Query the System
```bash
# Ask questions about your documents
python legal_rag_system.py query "What are the key terms in the assignment agreement?"

# Get more detailed results
python legal_rag_system.py query "Explain the indemnification clauses" --top-k 10
```

### 3. Analyze Specific Documents
```bash
# General analysis
python legal_rag_system.py analyze document.pdf

# Risk assessment
python legal_rag_system.py analyze document.pdf --analysis-type risks

# Compliance review
python legal_rag_system.py analyze document.pdf --analysis-type compliance
```

### 4. Check System Status
```bash
python legal_rag_system.py status
```

## 📁 Project Structure

```
legal-rag-system/
├── legal_rag_system.py      # Main application
├── config.py                # Configuration management
├── document_processor.py    # PDF processing and chunking
├── vector_store.py          # ChromaDB vector operations
├── retrieval_engine.py      # Advanced retrieval logic
├── llm_handler.py           # Saul-7B model integration
├── utils.py                 # Utility functions
├── setup.py                 # Installation script
├── requirements.txt         # Python dependencies
├── README.md               # This file
├── .env.example            # Environment configuration template
├── models/                 # Cached models
├── vector_db/              # Vector database storage
├── cache/                  # Processed document cache
├── logs/                   # System logs
└── examples/               # Example queries and outputs
```

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Model Configuration
LLM_MODEL_NAME=Equall/Saul-7B-Instruct-v1
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
MODEL_CACHE_DIR=./models

# Document Processing
DOCUMENTS_FOLDER=./kashewfinaltransactiondocuments
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Vector Database
VECTOR_DB_PATH=./vector_db
COLLECTION_NAME=legal_documents

# System Settings
LOG_LEVEL=INFO
ENABLE_GPU=true
```

### Advanced Configuration
Edit `config.py` to customize:
- Chunking strategies
- Retrieval parameters
- Model settings
- Legal document patterns

## 🔍 Usage Examples

### Interactive Python Usage
```python
from legal_rag_system import LegalRAGSystem

# Initialize the system
system = LegalRAGSystem()
system.initialize()

# Index documents
system.index_documents("./my_legal_docs")

# Query the system
result = system.query("What are the termination conditions?")
print(result["response"])

# Analyze a document
analysis = system.analyze_document("contract.pdf", "risks")
print(analysis)
```

### Example Queries
- "What are the key obligations in the assignment agreement?"
- "Explain the indemnification provisions across all documents"
- "What intellectual property rights are being transferred?"
- "Summarize the termination clauses in the contracts"
- "What are the governing law provisions?"

## 🧪 Testing

### Run System Tests
```bash
# Test document processing
python -c "from document_processor import DocumentProcessor; dp = DocumentProcessor(); print('✓ Document processor works')"

# Test vector store
python -c "from vector_store import VectorStore; vs = VectorStore(); print('✓ Vector store works')"

# Test LLM (requires more time)
python -c "from llm_handler import LegalLLMHandler; llm = LegalLLMHandler(); print('✓ LLM loaded')"
```

### Performance Benchmarking
```bash
# Time document indexing
time python legal_rag_system.py index

# Benchmark query performance
time python legal_rag_system.py query "test query"
```

## 🔧 Troubleshooting

### Common Issues

**Out of Memory Errors**
- Enable model quantization in config
- Reduce batch sizes
- Use CPU-only mode
- Close other applications

**Slow Performance**
- Enable GPU acceleration
- Increase batch sizes
- Use SSD storage
- Optimize chunk sizes

**Model Download Issues**
- Check internet connection
- Verify Hugging Face access
- Use manual model download
- Check disk space

**Document Processing Errors**
- Verify PDF file integrity
- Check file permissions
- Enable OCR for scanned documents
- Review error logs

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python legal_rag_system.py query "your query"

# Check logs
tail -f logs/legal_rag.log
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Equall** for the Saul-7B-Instruct-v1 legal language model
- **ChromaDB** for the vector database
- **Hugging Face** for the transformers library
- **Sentence Transformers** for embedding models

## 🌐 Web Interface

The system includes an optional web interface for easier interaction:

```bash
# Start the web interface
python web_interface.py

# Or with custom settings
python web_interface.py --host 0.0.0.0 --port 8080
```

Access the interface at `http://localhost:8000` for:
- Interactive document querying
- System status monitoring
- Document management
- Real-time response generation

## 🧪 Testing

### Quick Test
```bash
# Test core functionality (no LLM download)
python test_system.py

# Run specific component tests
python -m pytest test_system.py::LegalRAGTester::test_document_processor
```

### Full Test Suite
```bash
# Test everything including LLM (downloads model)
python test_system.py full

# Run example demonstrations
python examples/example_queries.py queries
python examples/example_queries.py analysis
python examples/example_queries.py capabilities
```

## 📊 Performance Optimization

### Memory Management
- **Enable Quantization**: Set `load_in_4bit=True` for GPU inference
- **Adjust Batch Sizes**: Reduce if experiencing OOM errors
- **Use CPU Mode**: Set `ENABLE_GPU=false` for systems without sufficient VRAM

### Speed Optimization
- **SSD Storage**: Use SSD for vector database and model cache
- **GPU Acceleration**: NVIDIA GPU with 8GB+ VRAM recommended
- **Parallel Processing**: Increase `MAX_WORKERS` for document processing

### Large Document Collections
- **Incremental Indexing**: Index documents in batches
- **Selective Retrieval**: Use document type filters
- **Cache Management**: Regular cleanup of processed document cache

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `logs/legal_rag.log`
3. Run the test suite: `python test_system.py`
4. Check system status: `python legal_rag_system.py status`
5. Open an issue with detailed error information and system specs

## 🔄 Updates and Maintenance

### Regular Maintenance
```bash
# Update vector embeddings for new documents
python legal_rag_system.py index --force

# Clear cache and rebuild
rm -rf cache/ vector_db/
python legal_rag_system.py index

# Update dependencies
pip install -r requirements.txt --upgrade
```

### Model Updates
The system automatically downloads model updates. To force a refresh:
```bash
rm -rf models/
python setup.py --skip-test
```

---

**Note**: This system is designed for legal document analysis and should be used as a tool to assist legal professionals. Always verify important information with qualified legal counsel.
