"""
Optional web interface for the Legal RAG System using FastAPI.
Provides a REST API and simple web UI for document querying.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from loguru import logger

from legal_rag_system import LegalRAGSystem
from config import config


# Pydantic models for API
class QueryRequest(BaseModel):
    query: str
    top_k: int = 5
    document_type: Optional[str] = None


class QueryResponse(BaseModel):
    query: str
    response: str
    sources: List[Dict[str, Any]]
    retrieval_time: float
    num_sources: int


class AnalysisRequest(BaseModel):
    document_path: str
    analysis_type: str = "general"


class SystemStatus(BaseModel):
    initialized: bool
    components: Dict[str, bool]
    vector_store_stats: Dict[str, Any]
    llm_info: Dict[str, Any]


# Initialize FastAPI app
app = FastAPI(
    title="Legal RAG System API",
    description="AI-powered legal document analysis and querying system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global system instance
legal_system: Optional[LegalRAGSystem] = None


@app.on_event("startup")
async def startup_event():
    """Initialize the legal system on startup."""
    global legal_system
    try:
        logger.info("Initializing Legal RAG System...")
        legal_system = LegalRAGSystem()
        legal_system.initialize()
        logger.info("Legal RAG System initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Legal RAG System: {e}")
        legal_system = None


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    global legal_system
    if legal_system:
        legal_system.cleanup()
        logger.info("Legal RAG System cleaned up")


# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface."""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Legal RAG System</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
            .query-section { margin-bottom: 30px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #34495e; }
            input[type="text"], textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
            textarea { height: 100px; resize: vertical; }
            button { background-color: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            button:hover { background-color: #2980b9; }
            button:disabled { background-color: #bdc3c7; cursor: not-allowed; }
            .response { margin-top: 20px; padding: 20px; background-color: #ecf0f1; border-radius: 5px; border-left: 4px solid #3498db; }
            .sources { margin-top: 15px; }
            .source-item { background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; border-left: 3px solid #28a745; }
            .loading { text-align: center; color: #7f8c8d; }
            .error { color: #e74c3c; background-color: #fadbd8; padding: 15px; border-radius: 5px; }
            .status { margin-bottom: 20px; padding: 15px; background-color: #d5f4e6; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏛️ Legal RAG System</h1>
            
            <div id="status" class="status">
                <strong>System Status:</strong> <span id="status-text">Checking...</span>
            </div>
            
            <div class="query-section">
                <h2>Query Legal Documents</h2>
                <form id="queryForm">
                    <div class="form-group">
                        <label for="query">Legal Query:</label>
                        <textarea id="query" name="query" placeholder="Enter your legal question here..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="top_k">Number of Results:</label>
                        <select id="top_k" name="top_k">
                            <option value="3">3</option>
                            <option value="5" selected>5</option>
                            <option value="10">10</option>
                        </select>
                    </div>
                    <button type="submit" id="submitBtn">Submit Query</button>
                </form>
                
                <div id="response" style="display: none;"></div>
            </div>
        </div>

        <script>
            // Check system status
            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const status = await response.json();
                    const statusText = document.getElementById('status-text');
                    
                    if (status.initialized) {
                        statusText.innerHTML = '✅ Ready';
                        statusText.style.color = '#27ae60';
                    } else {
                        statusText.innerHTML = '❌ Not Ready';
                        statusText.style.color = '#e74c3c';
                    }
                } catch (error) {
                    document.getElementById('status-text').innerHTML = '❌ Error';
                }
            }

            // Handle form submission
            document.getElementById('queryForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const submitBtn = document.getElementById('submitBtn');
                const responseDiv = document.getElementById('response');
                const query = document.getElementById('query').value;
                const topK = document.getElementById('top_k').value;
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.textContent = 'Processing...';
                responseDiv.style.display = 'block';
                responseDiv.innerHTML = '<div class="loading">🔍 Searching documents and generating response...</div>';
                
                try {
                    const response = await fetch('/api/query', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            query: query,
                            top_k: parseInt(topK)
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.error) {
                        responseDiv.innerHTML = `<div class="error">Error: ${result.error}</div>`;
                    } else {
                        let html = `
                            <div class="response">
                                <h3>Response:</h3>
                                <p>${result.response.replace(/\n/g, '<br>')}</p>
                                <div class="sources">
                                    <h4>Sources (${result.num_sources} documents, ${result.retrieval_time.toFixed(2)}s):</h4>
                        `;
                        
                        result.sources.forEach((source, index) => {
                            html += `
                                <div class="source-item">
                                    <strong>${index + 1}. ${source.metadata.document_name}</strong>
                                    <br><small>Type: ${source.metadata.document_type} | Score: ${source.score.toFixed(3)}</small>
                                </div>
                            `;
                        });
                        
                        html += '</div></div>';
                        responseDiv.innerHTML = html;
                    }
                } catch (error) {
                    responseDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Submit Query';
                }
            });

            // Check status on page load
            checkStatus();
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.post("/api/query", response_model=QueryResponse)
async def query_documents(request: QueryRequest):
    """Query the legal document system."""
    if not legal_system:
        raise HTTPException(status_code=503, detail="Legal system not initialized")
    
    try:
        result = legal_system.query(request.query, request.top_k)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return QueryResponse(**result)
        
    except Exception as e:
        logger.error(f"Query error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/analyze")
async def analyze_document(request: AnalysisRequest):
    """Analyze a specific document."""
    if not legal_system:
        raise HTTPException(status_code=503, detail="Legal system not initialized")
    
    if not os.path.exists(request.document_path):
        raise HTTPException(status_code=404, detail="Document not found")
    
    try:
        result = legal_system.analyze_document(request.document_path, request.analysis_type)
        return {"analysis": result}
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/status", response_model=SystemStatus)
async def get_system_status():
    """Get system status and statistics."""
    if not legal_system:
        return SystemStatus(
            initialized=False,
            components={},
            vector_store_stats={},
            llm_info={}
        )
    
    try:
        status = legal_system.get_system_status()
        return SystemStatus(**status)
        
    except Exception as e:
        logger.error(f"Status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/index")
async def index_documents(folder_path: str = Form(...), force_reindex: bool = Form(False)):
    """Index documents from a folder."""
    if not legal_system:
        raise HTTPException(status_code=503, detail="Legal system not initialized")
    
    try:
        # Run indexing in background
        legal_system.index_documents(folder_path, force_reindex)
        return {"message": "Indexing completed successfully"}
        
    except Exception as e:
        logger.error(f"Indexing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/documents")
async def list_documents():
    """List available documents."""
    docs_folder = config.document.documents_folder
    
    if not os.path.exists(docs_folder):
        return {"documents": []}
    
    documents = []
    for ext in config.document.supported_extensions:
        for file_path in Path(docs_folder).glob(f"*{ext}"):
            documents.append({
                "name": file_path.name,
                "path": str(file_path),
                "size": file_path.stat().st_size,
                "type": ext
            })
    
    return {"documents": documents}


def run_web_interface(host: str = "localhost", port: int = 8000, reload: bool = False):
    """Run the web interface."""
    logger.info(f"Starting Legal RAG System web interface at http://{host}:{port}")
    uvicorn.run(
        "web_interface:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


if __name__ == "__main__":
    import click
    
    @click.command()
    @click.option('--host', default='localhost', help='Host to bind to')
    @click.option('--port', default=8000, help='Port to bind to')
    @click.option('--reload', is_flag=True, help='Enable auto-reload for development')
    def main(host, port, reload):
        """Start the Legal RAG System web interface."""
        run_web_interface(host, port, reload)
    
    main()
