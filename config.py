"""
Configuration management for the Legal RAG System.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class ModelConfig(BaseSettings):
    """Configuration for ML models."""
    
    # LLM Configuration
    llm_model_name: str = "Equall/Saul-7B-Instruct-v1"
    llm_device: str = "auto"  # "auto", "cpu", "cuda"
    llm_max_length: int = 4096
    llm_temperature: float = 0.1
    llm_top_p: float = 0.9
    llm_do_sample: bool = True
    
    # Embedding Model Configuration
    embedding_model_name: str = "sentence-transformers/all-MiniLM-L6-v2"
    legal_embedding_model: str = "law-ai/InLegalBERT"  # Alternative legal-specific model
    embedding_device: str = "auto"
    embedding_batch_size: int = 32
    
    # Model caching
    model_cache_dir: str = "./models"
    use_model_cache: bool = True


class DocumentConfig(BaseSettings):
    """Configuration for document processing."""
    
    # Document paths
    documents_folder: str = "./kashewfinaltransactiondocuments"
    processed_docs_cache: str = "./cache/processed_docs"
    
    # Supported file types
    supported_extensions: List[str] = [".pdf", ".docx", ".txt"]
    
    # Chunking configuration
    chunk_size: int = 1000
    chunk_overlap: int = 200
    min_chunk_size: int = 100
    max_chunk_size: int = 2000
    
    # Legal document specific settings
    preserve_legal_structure: bool = True
    extract_citations: bool = True
    preserve_formatting: bool = True
    
    # OCR settings (if needed)
    enable_ocr: bool = False
    ocr_language: str = "eng"


class VectorStoreConfig(BaseSettings):
    """Configuration for vector database."""
    
    # ChromaDB settings
    vector_db_path: str = "./vector_db"
    collection_name: str = "legal_documents"
    
    # Search configuration
    similarity_threshold: float = 0.7
    max_results: int = 10
    enable_hybrid_search: bool = True
    
    # Indexing settings
    index_batch_size: int = 100
    embedding_dimension: int = 384  # For all-MiniLM-L6-v2


class RetrievalConfig(BaseSettings):
    """Configuration for retrieval system."""
    
    # Search parameters
    top_k: int = 5
    similarity_threshold: float = 0.75
    rerank_top_k: int = 3
    
    # Hybrid search weights
    semantic_weight: float = 0.7
    keyword_weight: float = 0.3
    
    # Legal-specific retrieval
    prioritize_citations: bool = True
    cross_reference_boost: float = 1.2
    section_context_window: int = 2  # Include adjacent sections


class SystemConfig(BaseSettings):
    """General system configuration."""
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "./logs/legal_rag.log"
    enable_file_logging: bool = True
    
    # Performance
    max_workers: int = 4
    memory_limit_gb: float = 8.0
    enable_gpu: bool = True
    
    # Security
    enable_content_filtering: bool = True
    max_query_length: int = 1000
    
    # API settings (for future web interface)
    api_host: str = "localhost"
    api_port: int = 8000
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class LegalRAGConfig:
    """Main configuration class that combines all settings."""
    
    def __init__(self, config_file: Optional[str] = None):
        if config_file and os.path.exists(config_file):
            load_dotenv(config_file)
        
        self.model = ModelConfig()
        self.document = DocumentConfig()
        self.vector_store = VectorStoreConfig()
        self.retrieval = RetrievalConfig()
        self.system = SystemConfig()
        
        # Create necessary directories
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.model.model_cache_dir,
            self.document.processed_docs_cache,
            self.vector_store.vector_db_path,
            os.path.dirname(self.system.log_file),
            "./cache",
            "./examples",
            "./logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary."""
        return {
            "model": self.model.dict(),
            "document": self.document.dict(),
            "vector_store": self.vector_store.dict(),
            "retrieval": self.retrieval.dict(),
            "system": self.system.dict()
        }
    
    def update_documents_folder(self, folder_path: str):
        """Update the documents folder path."""
        if os.path.exists(folder_path):
            self.document.documents_folder = folder_path
        else:
            raise ValueError(f"Documents folder does not exist: {folder_path}")


# Global configuration instance
config = LegalRAGConfig()


# Legal document type mappings
LEGAL_DOCUMENT_TYPES = {
    "contract": ["agreement", "contract", "terms"],
    "assignment": ["assignment", "transfer"],
    "release": ["release", "waiver"],
    "consent": ["consent", "approval"],
    "dissolution": ["dissolution", "termination"],
    "bill_of_sale": ["bill of sale", "purchase"],
    "intellectual_property": ["ip", "intellectual property", "patent", "trademark"],
    "safe": ["safe", "convertible"],
    "board_resolution": ["board", "resolution", "consent"],
    "shareholder": ["shareholder", "stockholder"]
}

# Legal section patterns for intelligent chunking
LEGAL_SECTION_PATTERNS = [
    r"ARTICLE\s+[IVX]+",
    r"Section\s+\d+",
    r"WHEREAS,",
    r"NOW, THEREFORE,",
    r"IN WITNESS WHEREOF",
    r"DEFINITIONS",
    r"REPRESENTATIONS AND WARRANTIES",
    r"COVENANTS",
    r"CONDITIONS",
    r"INDEMNIFICATION",
    r"MISCELLANEOUS"
]
