"""
Vector store module for the Legal RAG System.
Handles embedding generation and vector database operations using ChromaDB.
"""

import os
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from loguru import logger
from tqdm import tqdm

import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import torch

from config import config


class VectorStore:
    """Manages vector embeddings and similarity search using ChromaDB."""
    
    def __init__(self):
        self.db_path = Path(config.vector_store.vector_db_path)
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize ChromaDB
        self.client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Get or create collection
        try:
            self.collection = self.client.get_collection(
                name=config.vector_store.collection_name
            )
            logger.info(f"Loaded existing collection: {config.vector_store.collection_name}")
        except Exception:  # Catch any exception including NotFoundError
            self.collection = self.client.create_collection(
                name=config.vector_store.collection_name,
                metadata={"description": "Legal documents collection"}
            )
            logger.info(f"Created new collection: {config.vector_store.collection_name}")
        
        # Initialize embedding model
        self.embedding_model = None
        self._load_embedding_model()
        
        logger.info("VectorStore initialized")
    
    def _load_embedding_model(self):
        """Load the sentence transformer model for embeddings."""
        try:
            model_name = config.model.embedding_model_name
            logger.info(f"Loading embedding model: {model_name}")
            
            # Set device
            device = "cuda" if torch.cuda.is_available() and config.model.embedding_device != "cpu" else "cpu"
            
            self.embedding_model = SentenceTransformer(
                model_name,
                device=device,
                cache_folder=config.model.model_cache_dir
            )
            
            logger.info(f"Embedding model loaded on device: {device}")
            
        except Exception as e:
            logger.error(f"Error loading embedding model: {e}")
            raise
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        Add documents to the vector store.
        
        Args:
            documents: List of document chunks with text and metadata
        
        Returns:
            Success status
        """
        if not documents:
            logger.warning("No documents to add")
            return False
        
        logger.info(f"Adding {len(documents)} documents to vector store")
        
        try:
            # Prepare data for ChromaDB
            texts = []
            metadatas = []
            ids = []
            
            for doc in tqdm(documents, desc="Preparing documents"):
                # Generate unique ID if not provided
                doc_id = doc.get("id", str(uuid.uuid4()))
                
                texts.append(doc["text"])
                metadatas.append(doc["metadata"])
                ids.append(doc_id)
            
            # Generate embeddings in batches
            logger.info("Generating embeddings...")
            embeddings = self._generate_embeddings(texts)
            
            # Add to ChromaDB
            logger.info("Adding to vector database...")
            self.collection.add(
                embeddings=embeddings.tolist(),
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Successfully added {len(documents)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Error adding documents to vector store: {e}")
            return False
    
    def _generate_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Generate embeddings for a list of texts.
        
        Args:
            texts: List of text strings
        
        Returns:
            Numpy array of embeddings
        """
        batch_size = config.model.embedding_batch_size
        embeddings = []
        
        for i in tqdm(range(0, len(texts), batch_size), desc="Generating embeddings"):
            batch = texts[i:i + batch_size]
            batch_embeddings = self.embedding_model.encode(
                batch,
                convert_to_numpy=True,
                show_progress_bar=False
            )
            embeddings.append(batch_embeddings)
        
        return np.vstack(embeddings)
    
    def search(
        self,
        query: str,
        top_k: int = None,
        filter_metadata: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents using semantic similarity.
        
        Args:
            query: Search query string
            top_k: Number of results to return
            filter_metadata: Metadata filters for search
        
        Returns:
            List of search results with scores and metadata
        """
        if top_k is None:
            top_k = config.retrieval.top_k
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query], convert_to_numpy=True)[0]
            
            # Perform search
            results = self.collection.query(
                query_embeddings=[query_embedding.tolist()],
                n_results=top_k,
                where=filter_metadata
            )
            
            # Format results
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i in range(len(results["documents"][0])):
                    result = {
                        "id": results["ids"][0][i],
                        "text": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "score": 1 - results["distances"][0][i],  # Convert distance to similarity
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} results for query")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error during search: {e}")
            return []
    
    def hybrid_search(
        self,
        query: str,
        top_k: int = None,
        semantic_weight: float = None,
        keyword_weight: float = None,
        filter_metadata: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining semantic and keyword matching.
        
        Args:
            query: Search query string
            top_k: Number of results to return
            semantic_weight: Weight for semantic search
            keyword_weight: Weight for keyword search
            filter_metadata: Metadata filters
        
        Returns:
            List of search results with combined scores
        """
        if top_k is None:
            top_k = config.retrieval.top_k
        if semantic_weight is None:
            semantic_weight = config.retrieval.semantic_weight
        if keyword_weight is None:
            keyword_weight = config.retrieval.keyword_weight
        
        # Normalize weights
        total_weight = semantic_weight + keyword_weight
        semantic_weight /= total_weight
        keyword_weight /= total_weight
        
        # Get semantic search results
        semantic_results = self.search(query, top_k * 2, filter_metadata)
        
        # Get keyword search results
        keyword_results = self._keyword_search(query, top_k * 2, filter_metadata)
        
        # Combine and rank results
        combined_results = self._combine_search_results(
            semantic_results, keyword_results, semantic_weight, keyword_weight
        )
        
        return combined_results[:top_k]
    
    def _keyword_search(
        self,
        query: str,
        top_k: int,
        filter_metadata: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword-based search.
        
        Args:
            query: Search query
            top_k: Number of results
            filter_metadata: Metadata filters
        
        Returns:
            List of keyword search results
        """
        try:
            # Simple keyword matching using ChromaDB's built-in search
            # This is a basic implementation - could be enhanced with BM25 or other algorithms
            query_terms = query.lower().split()
            
            # Get all documents (with filter if provided)
            all_results = self.collection.get(
                where=filter_metadata,
                include=["documents", "metadatas"]
            )
            
            # Score documents based on keyword matches
            scored_results = []
            for i, doc in enumerate(all_results["documents"]):
                doc_lower = doc.lower()
                score = 0
                
                for term in query_terms:
                    # Count occurrences of each term
                    score += doc_lower.count(term)
                
                if score > 0:
                    scored_results.append({
                        "id": all_results["ids"][i],
                        "text": doc,
                        "metadata": all_results["metadatas"][i],
                        "score": score / len(query_terms)  # Normalize by query length
                    })
            
            # Sort by score and return top results
            scored_results.sort(key=lambda x: x["score"], reverse=True)
            return scored_results[:top_k]
            
        except Exception as e:
            logger.error(f"Error during keyword search: {e}")
            return []
    
    def _combine_search_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and keyword search results.
        
        Args:
            semantic_results: Results from semantic search
            keyword_results: Results from keyword search
            semantic_weight: Weight for semantic scores
            keyword_weight: Weight for keyword scores
        
        Returns:
            Combined and ranked results
        """
        # Create a dictionary to combine scores for the same documents
        combined_scores = {}
        
        # Add semantic scores
        for result in semantic_results:
            doc_id = result["id"]
            combined_scores[doc_id] = {
                "semantic_score": result["score"] * semantic_weight,
                "keyword_score": 0,
                "result": result
            }
        
        # Add keyword scores
        for result in keyword_results:
            doc_id = result["id"]
            if doc_id in combined_scores:
                combined_scores[doc_id]["keyword_score"] = result["score"] * keyword_weight
            else:
                combined_scores[doc_id] = {
                    "semantic_score": 0,
                    "keyword_score": result["score"] * keyword_weight,
                    "result": result
                }
        
        # Calculate combined scores and sort
        final_results = []
        for doc_id, scores in combined_scores.items():
            combined_score = scores["semantic_score"] + scores["keyword_score"]
            result = scores["result"].copy()
            result["score"] = combined_score
            result["semantic_score"] = scores["semantic_score"]
            result["keyword_score"] = scores["keyword_score"]
            final_results.append(result)
        
        final_results.sort(key=lambda x: x["score"], reverse=True)
        return final_results
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the vector store collection.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()
            
            # Get sample of documents to analyze
            sample_size = min(100, count)
            sample = self.collection.get(limit=sample_size, include=["metadatas"])
            
            # Analyze document types
            doc_types = {}
            for metadata in sample["metadatas"]:
                doc_type = metadata.get("document_type", "unknown")
                doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
            
            return {
                "total_documents": count,
                "document_types": doc_types,
                "collection_name": config.vector_store.collection_name,
                "embedding_model": config.model.embedding_model_name
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}
    
    def delete_collection(self):
        """Delete the entire collection."""
        try:
            self.client.delete_collection(config.vector_store.collection_name)
            logger.info("Collection deleted successfully")
        except Exception as e:
            logger.error(f"Error deleting collection: {e}")
    
    def reset_collection(self):
        """Reset the collection by deleting and recreating it."""
        try:
            self.delete_collection()
            self.collection = self.client.create_collection(
                name=config.vector_store.collection_name,
                metadata={"description": "Legal documents collection"}
            )
            logger.info("Collection reset successfully")
        except Exception as e:
            logger.error(f"Error resetting collection: {e}")
