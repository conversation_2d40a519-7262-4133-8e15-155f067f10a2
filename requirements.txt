# Core ML and NLP libraries
torch>=2.0.0
transformers>=4.35.0
sentence-transformers>=2.2.2
accelerate>=0.24.0

# Vector database and embeddings
chromadb>=0.4.15
faiss-cpu>=1.7.4

# Document processing
PyMuPDF>=1.23.0
pypdf>=3.17.0
python-docx>=0.8.11
pdfplumber>=0.9.0

# Text processing and NLP
spacy>=3.7.0
nltk>=3.8.1
langchain>=0.0.350
langchain-community>=0.0.10

# Data handling and utilities
pandas>=2.1.0
numpy>=1.24.0
scikit-learn>=1.3.0
tqdm>=4.66.0

# Configuration and logging
pydantic>=2.5.0
python-dotenv>=1.0.0
loguru>=0.7.2

# API and web framework (optional for future web interface)
fastapi>=0.104.0
uvicorn>=0.24.0

# Development and testing
pytest>=7.4.0
black>=23.10.0
flake8>=6.1.0

# Additional utilities
click>=8.1.7
rich>=13.7.0
pathlib2>=2.3.7
