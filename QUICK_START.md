# Legal RAG System - Quick Start Guide

## 🚀 Getting Started

### 1. Installation
```bash
# Run the automated setup
python setup.py

# This will:
# - Install all dependencies
# - Download required models
# - Create necessary directories
# - Set up configuration
```

### 2. Index Your Documents
```bash
# Index the Kashew transaction documents
python legal_rag_system.py index

# Check indexing status
python legal_rag_system.py status
```

### 3. Example Queries for Your Kashew Documents

Try these queries with your transaction documents:

#### General Contract Analysis
```bash
python legal_rag_system.py query "What are the key terms and conditions in the assignment agreements?"

python legal_rag_system.py query "Summarize the main provisions of the asset purchase agreement"

python legal_rag_system.py query "What intellectual property rights are being transferred in these transactions?"
```

#### Risk and Liability Analysis
```bash
python legal_rag_system.py query "What indemnification provisions are included in these agreements?"

python legal_rag_system.py query "What are the potential risks and liabilities for each party?"

python legal_rag_system.py query "Are there any warranty disclaimers or limitations of liability?"
```

#### Transaction Structure Analysis
```bash
python legal_rag_system.py query "Explain the overall structure of the Kashew to Palm transaction"

python legal_rag_system.py query "What assets are being transferred in the bill of sale?"

python legal_rag_system.py query "How is the dissolution of Kashew being handled?"
```

#### Compliance and Governance
```bash
python legal_rag_system.py query "What board and shareholder approvals were required?"

python legal_rag_system.py query "Are there any regulatory compliance requirements mentioned?"

python legal_rag_system.py query "What are the governing law and jurisdiction provisions?"
```

#### Financial Terms
```bash
python legal_rag_system.py query "What consideration is being paid in the asset purchase?"

python legal_rag_system.py query "How are the SAFE agreements being handled in the transaction?"

python legal_rag_system.py query "Are there any escrow or holdback provisions?"
```

### 4. Document Analysis

Analyze specific documents:

```bash
# General analysis
python legal_rag_system.py analyze "kashewfinaltransactiondocuments/Palm Venture Studios - Asset Purchase Agreement (Kashew) (Execution Version).pdf"

# Risk assessment
python legal_rag_system.py analyze "kashewfinaltransactiondocuments/Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf" --analysis-type risks

# Compliance review
python legal_rag_system.py analyze "kashewfinaltransactiondocuments/Kashew - Board Consent (Asset Sale and Dissolution) (Execution Version).pdf" --analysis-type compliance
```

### 5. Web Interface

For a more user-friendly experience:

```bash
# Start the web interface
python web_interface.py

# Access at http://localhost:8000
```

The web interface provides:
- Interactive query interface
- Real-time response generation
- Source document references
- System status monitoring

### 6. Testing

Verify everything works:

```bash
# Quick test (no LLM download)
python test_system.py

# Full test including LLM
python test_system.py full

# Run example demonstrations
python examples/example_queries.py capabilities
```

## 🔧 Configuration

### Environment Variables (.env)

Create a `.env` file to customize settings:

```bash
# Copy the example
cp .env.example .env

# Edit with your preferences
nano .env
```

Key settings:
- `DOCUMENTS_FOLDER`: Path to your legal documents
- `CHUNK_SIZE`: Size of document chunks (default: 1000)
- `ENABLE_GPU`: Use GPU acceleration (default: true)
- `LOG_LEVEL`: Logging verbosity (INFO, DEBUG, etc.)

### Hardware Recommendations

**Minimum Requirements:**
- Python 3.8+
- 8GB RAM
- 20GB free disk space

**Recommended for Optimal Performance:**
- 16GB+ RAM
- NVIDIA GPU with 8GB+ VRAM
- SSD storage
- Multi-core CPU

## 🎯 Expected Results

With your Kashew transaction documents, you should expect:

1. **Fast Indexing**: ~2-5 minutes for all 10 documents
2. **Accurate Retrieval**: Relevant document sections for legal queries
3. **Contextual Responses**: AI-generated answers with source citations
4. **Legal Domain Expertise**: Understanding of legal terminology and concepts

## 🔍 Sample Output

When you query "What are the key terms in the assignment agreements?", you might see:

```
Response to: What are the key terms in the assignment agreements?

Based on the assignment agreements in the documents, the key terms include:

1. **Assignment of Rights**: The agreements transfer all rights, title, and interest in specified assets from the assignor to the assignee.

2. **Intellectual Property**: Comprehensive assignment of intellectual property rights, including patents, trademarks, copyrights, and trade secrets.

3. **Consideration**: The agreements specify the consideration being paid for the assignment of rights.

4. **Representations and Warranties**: Both parties provide representations regarding their authority to enter into the agreement and the validity of the assigned rights.

5. **Indemnification**: Provisions for indemnification against claims related to the assigned assets.

**Sources:**
1. Kashew - Assignment and Release (B. Bolla) (Execution Version).pdf (Page 1)
2. Kashew - Palm - Assignment and Assumption Agreement (Execution Version).pdf (Page 2)
3. Kashew - Palm - Intellectual Property Assignment Agreement (Execution Version).pdf (Page 1)

Retrieved 3 sources in 1.23s
```

## 🆘 Troubleshooting

### Common Issues

**"Out of memory" errors:**
```bash
# Use CPU mode
export ENABLE_GPU=false
python legal_rag_system.py query "your query"
```

**Model download issues:**
```bash
# Check internet connection and try again
python setup.py --skip-test
```

**No documents found:**
```bash
# Verify document path
python legal_rag_system.py status
```

### Getting Help

1. Check the logs: `tail -f logs/legal_rag.log`
2. Run diagnostics: `python test_system.py`
3. Check system status: `python legal_rag_system.py status`

## 🎉 Next Steps

1. **Explore the System**: Try different types of queries
2. **Add More Documents**: Place additional PDFs in the documents folder and reindex
3. **Customize Configuration**: Adjust settings in `.env` for your needs
4. **Use the Web Interface**: More convenient for interactive use
5. **Integrate into Workflows**: Use the Python API for custom applications

---

**Ready to start? Run `python setup.py` and begin exploring your legal documents with AI!**
