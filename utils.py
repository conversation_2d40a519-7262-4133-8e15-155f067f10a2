"""
Utility functions for the Legal RAG System.
"""

import os
import re
import hashlib
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging
from loguru import logger

from config import config, LEGAL_SECTION_PATTERNS, LEGAL_DOCUMENT_TYPES


def setup_logging():
    """Set up logging configuration."""
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=config.system.log_level
    )
    
    # Add file handler if enabled
    if config.system.enable_file_logging:
        logger.add(
            config.system.log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=config.system.log_level,
            rotation="10 MB",
            retention="30 days"
        )


def get_file_hash(file_path: str) -> str:
    """Generate MD5 hash of a file for caching purposes."""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"Error generating hash for {file_path}: {e}")
        return ""


def extract_legal_document_type(filename: str, content: str = "") -> str:
    """
    Extract the type of legal document based on filename and content.
    
    Args:
        filename: Name of the document file
        content: Document content (optional)
    
    Returns:
        Document type string
    """
    filename_lower = filename.lower()
    content_lower = content.lower()
    
    # Check filename patterns
    for doc_type, keywords in LEGAL_DOCUMENT_TYPES.items():
        for keyword in keywords:
            if keyword in filename_lower or keyword in content_lower:
                return doc_type
    
    return "general_legal"


def extract_legal_citations(text: str) -> List[str]:
    """
    Extract legal citations from text using regex patterns.
    
    Args:
        text: Input text to search for citations
    
    Returns:
        List of found citations
    """
    citation_patterns = [
        r'\d+\s+U\.S\.C\.\s+§\s+\d+',  # USC citations
        r'\d+\s+F\.\d+\s+\d+',  # Federal reporter citations
        r'\d+\s+S\.Ct\.\s+\d+',  # Supreme Court citations
        r'Section\s+\d+(\.\d+)*',  # Section references
        r'Article\s+[IVX]+',  # Article references
    ]
    
    citations = []
    for pattern in citation_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        citations.extend(matches)
    
    return list(set(citations))  # Remove duplicates


def detect_legal_sections(text: str) -> List[Dict[str, Any]]:
    """
    Detect legal document sections using predefined patterns.
    
    Args:
        text: Document text
    
    Returns:
        List of detected sections with positions
    """
    sections = []
    
    for pattern in LEGAL_SECTION_PATTERNS:
        matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
        for match in matches:
            sections.append({
                "type": pattern,
                "text": match.group(),
                "start": match.start(),
                "end": match.end()
            })
    
    # Sort by position
    sections.sort(key=lambda x: x["start"])
    return sections


def clean_legal_text(text: str) -> str:
    """
    Clean and normalize legal document text.
    
    Args:
        text: Raw text from document
    
    Returns:
        Cleaned text
    """
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove page numbers and headers/footers
    text = re.sub(r'Page\s+\d+\s+of\s+\d+', '', text, flags=re.IGNORECASE)
    text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)
    
    # Normalize quotes
    text = re.sub(r'["""]', '"', text)
    text = re.sub(r"[''']", "'", text)
    
    # Remove extra line breaks but preserve paragraph structure
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    
    return text.strip()


def chunk_legal_document(
    text: str, 
    chunk_size: int = None, 
    chunk_overlap: int = None,
    preserve_structure: bool = True
) -> List[Dict[str, Any]]:
    """
    Intelligently chunk legal documents preserving structure.
    
    Args:
        text: Document text
        chunk_size: Maximum chunk size
        chunk_overlap: Overlap between chunks
        preserve_structure: Whether to preserve legal document structure
    
    Returns:
        List of chunks with metadata
    """
    if chunk_size is None:
        chunk_size = config.document.chunk_size
    if chunk_overlap is None:
        chunk_overlap = config.document.chunk_overlap
    
    chunks = []
    
    if preserve_structure:
        # Detect sections first
        sections = detect_legal_sections(text)
        
        if sections:
            # Chunk by sections
            current_pos = 0
            for i, section in enumerate(sections):
                # Get text from current position to section start
                if section["start"] > current_pos:
                    section_text = text[current_pos:section["start"]].strip()
                    if section_text:
                        chunks.extend(_create_chunks_from_text(
                            section_text, chunk_size, chunk_overlap, 
                            section_type="content"
                        ))
                
                # Get section text
                next_section_start = sections[i + 1]["start"] if i + 1 < len(sections) else len(text)
                section_text = text[section["start"]:next_section_start].strip()
                
                if section_text:
                    chunks.extend(_create_chunks_from_text(
                        section_text, chunk_size, chunk_overlap,
                        section_type=section["type"]
                    ))
                
                current_pos = next_section_start
        else:
            # No sections detected, use paragraph-based chunking
            chunks = _create_chunks_from_text(text, chunk_size, chunk_overlap)
    else:
        # Simple chunking
        chunks = _create_chunks_from_text(text, chunk_size, chunk_overlap)
    
    return chunks


def _create_chunks_from_text(
    text: str, 
    chunk_size: int, 
    chunk_overlap: int,
    section_type: str = "content"
) -> List[Dict[str, Any]]:
    """Helper function to create chunks from text."""
    chunks = []
    
    # Split by paragraphs first
    paragraphs = text.split('\n\n')
    
    current_chunk = ""
    current_size = 0
    
    for paragraph in paragraphs:
        paragraph = paragraph.strip()
        if not paragraph:
            continue
        
        paragraph_size = len(paragraph)
        
        # If paragraph alone exceeds chunk size, split it
        if paragraph_size > chunk_size:
            if current_chunk:
                citations = extract_legal_citations(current_chunk)
                chunks.append({
                    "text": current_chunk.strip(),
                    "size": current_size,
                    "section_type": section_type,
                    "citations": citations
                })
                current_chunk = ""
                current_size = 0
            
            # Split large paragraph
            words = paragraph.split()
            temp_chunk = ""
            for word in words:
                if len(temp_chunk + " " + word) > chunk_size:
                    if temp_chunk:
                        citations = extract_legal_citations(temp_chunk)
                        chunks.append({
                            "text": temp_chunk.strip(),
                            "size": len(temp_chunk),
                            "section_type": section_type,
                            "citations": citations
                        })
                    temp_chunk = word
                else:
                    temp_chunk += " " + word if temp_chunk else word
            
            if temp_chunk:
                current_chunk = temp_chunk
                current_size = len(temp_chunk)
        
        # If adding paragraph would exceed chunk size, save current chunk
        elif current_size + paragraph_size > chunk_size:
            if current_chunk:
                citations = extract_legal_citations(current_chunk)
                chunks.append({
                    "text": current_chunk.strip(),
                    "size": current_size,
                    "section_type": section_type,
                    "citations": citations
                })
            
            # Start new chunk with overlap
            if chunk_overlap > 0 and chunks:
                overlap_text = current_chunk[-chunk_overlap:] if len(current_chunk) > chunk_overlap else current_chunk
                current_chunk = overlap_text + "\n\n" + paragraph
                current_size = len(current_chunk)
            else:
                current_chunk = paragraph
                current_size = paragraph_size
        else:
            # Add paragraph to current chunk
            current_chunk += "\n\n" + paragraph if current_chunk else paragraph
            current_size += paragraph_size
    
    # Add final chunk
    if current_chunk:
        citations = extract_legal_citations(current_chunk)
        chunks.append({
            "text": current_chunk.strip(),
            "size": current_size,
            "section_type": section_type,
            "citations": citations
        })
    
    return chunks


def save_cache(data: Any, cache_file: str):
    """Save data to cache file."""
    try:
        cache_path = Path(cache_file)
        cache_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.debug(f"Cache saved to {cache_file}")
    except Exception as e:
        logger.error(f"Error saving cache to {cache_file}: {e}")


def load_cache(cache_file: str) -> Optional[Any]:
    """Load data from cache file."""
    try:
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.debug(f"Cache loaded from {cache_file}")
            return data
    except Exception as e:
        logger.error(f"Error loading cache from {cache_file}: {e}")
    
    return None


def format_legal_response(response: str, sources: List[Dict[str, Any]]) -> str:
    """
    Format the response with proper legal citations and sources.
    
    Args:
        response: Generated response text
        sources: List of source documents with metadata
    
    Returns:
        Formatted response with citations
    """
    formatted_response = response
    
    if sources:
        formatted_response += "\n\n**Sources:**\n"
        for i, source in enumerate(sources, 1):
            doc_name = source.get("document_name", "Unknown Document")
            page = source.get("page_number", "N/A")
            section = source.get("section_type", "content")
            
            formatted_response += f"{i}. {doc_name}"
            if page != "N/A":
                formatted_response += f" (Page {page})"
            if section != "content":
                formatted_response += f" - {section}"
            formatted_response += "\n"
    
    return formatted_response


def validate_query(query: str) -> Tuple[bool, str]:
    """
    Validate user query for security and content filtering.
    
    Args:
        query: User query string
    
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not query or not query.strip():
        return False, "Query cannot be empty"
    
    if len(query) > config.system.max_query_length:
        return False, f"Query too long. Maximum length is {config.system.max_query_length} characters"
    
    # Add more validation rules as needed
    return True, ""


# Initialize logging when module is imported
setup_logging()
