"""
LLM handler module for the Legal RAG System.
Manages the Equall/Saul-7B-Instruct-v1 model for legal text generation.
"""

import torch
import signal
import threading
import time
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    BitsAndBytesConfig, pipeline
)
from typing import List, Dict, Any, Optional
from loguru import logger
import gc

from config import config


class TimeoutError(Exception):
    """Custom timeout exception."""
    pass


def timeout_handler(signum, frame):
    """Handle timeout signal."""
    raise TimeoutError("Operation timed out")


class LegalLLMHandler:
    """Handles the legal LLM for response generation."""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = self._get_device()
        
        # Load model
        self._load_model()
        
        logger.info("LegalLLMHandler initialized")
    
    def _get_device(self) -> str:
        """Determine the best device for model inference."""
        if config.model.llm_device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        else:
            return config.model.llm_device
    
    def _load_model(self):
        """Load the Saul-7B model and tokenizer."""
        try:
            model_name = config.model.llm_model_name
            logger.info(f"Loading LLM model: {model_name}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                cache_dir=config.model.model_cache_dir,
                trust_remote_code=True
            )
            
            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Configure model loading based on available memory
            model_kwargs = {
                "cache_dir": config.model.model_cache_dir,
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device != "cpu" else torch.float32,
            }
            
            # Use quantization for GPU if available
            if self.device == "cuda":
                try:
                    quantization_config = BitsAndBytesConfig(
                        load_in_4bit=True,
                        bnb_4bit_compute_dtype=torch.float16,
                        bnb_4bit_use_double_quant=True,
                        bnb_4bit_quant_type="nf4"
                    )
                    model_kwargs["quantization_config"] = quantization_config
                    logger.info("Using 4-bit quantization for GPU")
                except Exception as e:
                    logger.warning(f"Quantization failed, loading without: {e}")
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                **model_kwargs
            )
            
            # Move to device if not using quantization
            if "quantization_config" not in model_kwargs:
                self.model = self.model.to(self.device)
            
            # Create pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device != "cpu" else torch.float32,
            )
            
            logger.info(f"Model loaded successfully on device: {self.device}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def generate_response(
        self,
        query: str,
        context_documents: List[Dict[str, Any]],
        max_length: int = None,
        temperature: float = None
    ) -> str:
        """
        Generate a response using the legal LLM with retrieved context.
        
        Args:
            query: User query
            context_documents: Retrieved documents for context
            max_length: Maximum response length
            temperature: Sampling temperature
        
        Returns:
            Generated response
        """
        if max_length is None:
            max_length = config.model.llm_max_length
        if temperature is None:
            temperature = config.model.llm_temperature
        
        try:
            # Prepare the prompt
            prompt = self._create_legal_prompt(query, context_documents)
            
            # Generate response with performance optimizations
            generation_kwargs = {
                "max_new_tokens": getattr(config.model, 'llm_max_new_tokens', 512),
                "temperature": temperature,
                "top_p": config.model.llm_top_p,
                "do_sample": config.model.llm_do_sample,
                "pad_token_id": self.tokenizer.eos_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "return_full_text": False,
                "repetition_penalty": 1.1,  # Prevent repetition
                "length_penalty": 1.0,      # Encourage appropriate length
            }

            # Set up timeout for generation (5 minutes max)
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(300)  # 5 minute timeout

            try:
                response = self.pipeline(prompt, **generation_kwargs)
                generated_text = response[0]["generated_text"]
            except TimeoutError:
                logger.error("LLM generation timed out after 5 minutes")
                return "I apologize, but the response generation took too long. Please try a simpler query or try again later."
            finally:
                signal.alarm(0)  # Cancel the alarm
                signal.signal(signal.SIGALRM, old_handler)  # Restore old handler
            
            # Clean up the response
            cleaned_response = self._clean_response(generated_text)
            
            return cleaned_response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I encountered an error while generating a response. Please try again."
    
    def _create_legal_prompt(
        self,
        query: str,
        context_documents: List[Dict[str, Any]]
    ) -> str:
        """
        Create a legal-specific prompt for the LLM.
        
        Args:
            query: User query
            context_documents: Retrieved context documents
        
        Returns:
            Formatted prompt
        """
        # Build context from retrieved documents
        context_parts = []
        for i, doc in enumerate(context_documents[:5]):  # Limit to top 5 documents
            doc_name = doc["metadata"].get("document_name", f"Document {i+1}")
            doc_type = doc["metadata"].get("document_type", "legal document")
            text = doc["text"]
            
            context_parts.append(f"[Document: {doc_name} ({doc_type})]\n{text}\n")
        
        context = "\n".join(context_parts)
        
        # Create the prompt using legal-specific instructions
        prompt = f"""You are a legal AI assistant specialized in analyzing legal documents. You have access to relevant legal documents and should provide accurate, helpful responses based on the provided context.

CONTEXT DOCUMENTS:
{context}

QUERY: {query}

INSTRUCTIONS:
1. Analyze the provided legal documents carefully
2. Provide a comprehensive answer based on the context
3. Cite specific documents when referencing information
4. If the context doesn't contain sufficient information, clearly state this
5. Use precise legal language and terminology
6. Highlight any important legal implications or considerations
7. If applicable, mention relevant legal principles or precedents

RESPONSE:"""

        return prompt
    
    def _clean_response(self, response: str) -> str:
        """
        Clean and format the generated response.
        
        Args:
            response: Raw generated response
        
        Returns:
            Cleaned response
        """
        # Remove any prompt artifacts
        response = response.strip()
        
        # Remove common generation artifacts
        artifacts = [
            "RESPONSE:",
            "Response:",
            "Answer:",
            "ANSWER:",
        ]
        
        for artifact in artifacts:
            if response.startswith(artifact):
                response = response[len(artifact):].strip()
        
        # Remove excessive whitespace
        response = " ".join(response.split())
        
        return response
    
    def analyze_legal_document(
        self,
        document_text: str,
        analysis_type: str = "general"
    ) -> str:
        """
        Analyze a legal document using the LLM.
        
        Args:
            document_text: Text of the legal document
            analysis_type: Type of analysis to perform
        
        Returns:
            Analysis results
        """
        analysis_prompts = {
            "general": "Provide a comprehensive analysis of this legal document, including its purpose, key terms, and important provisions.",
            "risks": "Identify potential legal risks and concerns in this document.",
            "summary": "Provide a concise summary of this legal document's main points.",
            "compliance": "Analyze this document for compliance issues and regulatory considerations."
        }
        
        prompt = f"""You are a legal AI assistant. Please analyze the following legal document.

DOCUMENT:
{document_text[:2000]}  # Limit document length

ANALYSIS REQUEST: {analysis_prompts.get(analysis_type, analysis_prompts["general"])}

ANALYSIS:"""
        
        try:
            response = self.pipeline(
                prompt,
                max_length=config.model.llm_max_length,
                temperature=0.1,  # Lower temperature for analysis
                top_p=config.model.llm_top_p,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            return self._clean_response(response[0]["generated_text"])
            
        except Exception as e:
            logger.error(f"Error analyzing document: {e}")
            return "Error occurred during document analysis."
    
    def extract_key_terms(self, document_text: str) -> List[str]:
        """
        Extract key legal terms from a document.
        
        Args:
            document_text: Text of the legal document
        
        Returns:
            List of key terms
        """
        prompt = f"""Extract the key legal terms and concepts from this document. Return only a comma-separated list of terms.

DOCUMENT:
{document_text[:1500]}

KEY TERMS:"""
        
        try:
            response = self.pipeline(
                prompt,
                max_length=200,
                temperature=0.1,
                do_sample=False,
                pad_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            terms_text = self._clean_response(response[0]["generated_text"])
            terms = [term.strip() for term in terms_text.split(",") if term.strip()]
            
            return terms[:10]  # Limit to top 10 terms
            
        except Exception as e:
            logger.error(f"Error extracting key terms: {e}")
            return []
    
    def cleanup(self):
        """Clean up model resources."""
        try:
            if self.model is not None:
                del self.model
            if self.tokenizer is not None:
                del self.tokenizer
            if self.pipeline is not None:
                del self.pipeline
            
            # Clear GPU cache if using CUDA
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # Force garbage collection
            gc.collect()
            
            logger.info("Model resources cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": config.model.llm_model_name,
            "device": self.device,
            "max_length": config.model.llm_max_length,
            "temperature": config.model.llm_temperature,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None
        }
