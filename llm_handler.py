"""
LLM handler module for the Legal RAG System.
Manages the Equall/Saul-7B-Instruct-v1 model for legal text generation.
"""

import torch
import signal
import threading
import time
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    BitsAndBytesConfig, pipeline
)
from typing import List, Dict, Any, Optional
from loguru import logger
import gc
import os

# Import llama-cpp-python for GGUF models
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    logger.warning("llama-cpp-python not available. GGUF models will not work.")

from config import config


class TimeoutError(Exception):
    """Custom timeout exception."""
    pass


def timeout_handler(signum, frame):
    """Handle timeout signal."""
    raise TimeoutError("Operation timed out")


class LegalLLMHandler:
    """Handles the legal LLM for response generation."""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = self._get_device()
        
        # Load model
        self._load_model()
        
        logger.info("LegalLLMHandler initialized")
    
    def _get_device(self) -> str:
        """Determine the best device for model inference."""
        if config.model.llm_device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        else:
            return config.model.llm_device
    
    def _load_model(self):
        """Load the legal LLM model (supports both transformers and GGUF)."""
        try:
            model_name = config.model.llm_model_name
            logger.info(f"Loading LLM model: {model_name}")

            # Check if we should use GGUF format
            use_gguf = getattr(config.model, 'llm_use_gguf', False)

            if use_gguf and LLAMA_CPP_AVAILABLE:
                self._load_gguf_model()
            else:
                self._load_transformers_model()

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    def _load_gguf_model(self):
        """Load GGUF model using llama-cpp-python."""
        model_name = config.model.llm_model_name
        model_file = getattr(config.model, 'llm_model_file', None)

        if not model_file:
            raise ValueError("llm_model_file must be specified for GGUF models")

        logger.info(f"Loading GGUF model: {model_name}/{model_file}")

        # Download model file if needed
        from huggingface_hub import hf_hub_download

        try:
            model_path = hf_hub_download(
                repo_id=model_name,
                filename=model_file,
                cache_dir=config.model.model_cache_dir
            )
            logger.info(f"Model file downloaded to: {model_path}")
        except Exception as e:
            logger.error(f"Failed to download model file: {e}")
            raise

        # Configure llama-cpp-python parameters
        n_gpu_layers = 35 if self.device == "mps" else 0  # Use Metal on Apple Silicon
        n_ctx = getattr(config.model, 'llm_max_length', 512)

        # Load the model
        self.model = Llama(
            model_path=model_path,
            n_ctx=n_ctx,
            n_threads=8,  # Optimize for Apple Silicon
            n_gpu_layers=n_gpu_layers,
            verbose=False
        )

        # For GGUF models, we don't need separate tokenizer and pipeline
        self.tokenizer = None
        self.pipeline = None

        logger.info(f"GGUF model loaded successfully with {n_gpu_layers} GPU layers")

    def _load_transformers_model(self):
        """Load model using transformers library."""
        model_name = config.model.llm_model_name

        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            cache_dir=config.model.model_cache_dir,
            trust_remote_code=True
        )

        # Set pad token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # Configure model loading with memory optimization
        torch_dtype = torch.bfloat16 if hasattr(config.model, 'llm_torch_dtype') and config.model.llm_torch_dtype == "bfloat16" else torch.float16
        if self.device == "cpu":
            torch_dtype = torch.float32

        model_kwargs = {
            "cache_dir": config.model.model_cache_dir,
            "trust_remote_code": True,
            "torch_dtype": torch_dtype,
            "device_map": "auto",  # Automatic device mapping
            "low_cpu_mem_usage": True,  # Reduce CPU memory usage
        }

        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            **model_kwargs
        )

        # Create pipeline with automatic device handling
        self.pipeline = pipeline(
            "text-generation",
            model=self.model,
            tokenizer=self.tokenizer,
            torch_dtype=torch_dtype,
        )

        logger.info(f"Transformers model loaded successfully on device: {self.device}")
    
    def generate_response(
        self,
        query: str,
        context_documents: List[Dict[str, Any]],
        max_length: int = None,
        temperature: float = None
    ) -> str:
        """
        Generate a response using the legal LLM with retrieved context.
        
        Args:
            query: User query
            context_documents: Retrieved documents for context
            max_length: Maximum response length
            temperature: Sampling temperature
        
        Returns:
            Generated response
        """
        if max_length is None:
            max_length = config.model.llm_max_length
        if temperature is None:
            temperature = config.model.llm_temperature
        
        try:
            # Prepare the prompt
            prompt = self._create_legal_prompt(query, context_documents)

            # Check if using GGUF model
            use_gguf = getattr(config.model, 'llm_use_gguf', False)

            if use_gguf and self.pipeline is None:
                # Use llama-cpp-python for GGUF models
                generated_text = self._generate_with_gguf(prompt, temperature)
            else:
                # Use transformers pipeline
                generated_text = self._generate_with_transformers(prompt, temperature)

            # Clean up the response
            cleaned_response = self._clean_response(generated_text)

            return cleaned_response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I encountered an error while generating a response. Please try again."

    def _generate_with_gguf(self, prompt: str, temperature: float) -> str:
        """Generate response using GGUF model with llama-cpp-python."""
        try:
            max_tokens = getattr(config.model, 'llm_max_new_tokens', 128)

            response = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=config.model.llm_top_p,
                repeat_penalty=1.1,
                stop=["</s>", "\n\n"],  # Stop tokens
                echo=False
            )

            return response['choices'][0]['text']

        except Exception as e:
            logger.error(f"Error with GGUF generation: {e}")
            raise

    def _generate_with_transformers(self, prompt: str, temperature: float) -> str:
        """Generate response using transformers pipeline."""
        try:
            generation_kwargs = {
                "max_new_tokens": getattr(config.model, 'llm_max_new_tokens', 512),
                "temperature": temperature,
                "top_p": config.model.llm_top_p,
                "do_sample": config.model.llm_do_sample,
                "pad_token_id": self.tokenizer.eos_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "return_full_text": False,
                "repetition_penalty": 1.1,
                "length_penalty": 1.0,
            }

            response = self.pipeline(prompt, **generation_kwargs)
            return response[0]["generated_text"]

        except Exception as e:
            logger.error(f"Error with transformers generation: {e}")
            raise

    def _create_legal_prompt(
        self,
        query: str,
        context_documents: List[Dict[str, Any]]
    ) -> str:
        """
        Create a legal-specific prompt for the LLM.
        
        Args:
            query: User query
            context_documents: Retrieved context documents
        
        Returns:
            Formatted prompt
        """
        # Build context from retrieved documents
        context_parts = []
        for i, doc in enumerate(context_documents[:5]):  # Limit to top 5 documents
            doc_name = doc["metadata"].get("document_name", f"Document {i+1}")
            doc_type = doc["metadata"].get("document_type", "legal document")
            text = doc["text"]
            
            context_parts.append(f"[Document: {doc_name} ({doc_type})]\n{text}\n")
        
        context = "\n".join(context_parts)
        
        # Create the prompt using legal-specific instructions
        prompt = f"""You are a legal AI assistant specialized in analyzing legal documents. You have access to relevant legal documents and should provide accurate, helpful responses based on the provided context.

CONTEXT DOCUMENTS:
{context}

QUERY: {query}

INSTRUCTIONS:
1. Analyze the provided legal documents carefully
2. Provide a comprehensive answer based on the context
3. Cite specific documents when referencing information
4. If the context doesn't contain sufficient information, clearly state this
5. Use precise legal language and terminology
6. Highlight any important legal implications or considerations
7. If applicable, mention relevant legal principles or precedents

RESPONSE:"""

        return prompt
    
    def _clean_response(self, response: str) -> str:
        """
        Clean and format the generated response.

        Args:
            response: Raw generated response

        Returns:
            Cleaned response
        """
        # Remove any prompt artifacts
        response = response.strip()

        # Remove common generation artifacts and model-specific tokens
        artifacts = [
            "RESPONSE:",
            "Response:",
            "Answer:",
            "ANSWER:",
            "[INST]",
            "[/INST]",
            "<s>",
            "</s>",
            "Human:",
            "Assistant:",
        ]

        for artifact in artifacts:
            response = response.replace(artifact, "").strip()

        # Remove excessive whitespace
        response = " ".join(response.split())

        # Escape any remaining markup for Rich display
        response = response.replace("[", "\\[").replace("]", "\\]")

        return response
    
    def analyze_legal_document(
        self,
        document_text: str,
        analysis_type: str = "general"
    ) -> str:
        """
        Analyze a legal document using the LLM.
        
        Args:
            document_text: Text of the legal document
            analysis_type: Type of analysis to perform
        
        Returns:
            Analysis results
        """
        analysis_prompts = {
            "general": "Provide a comprehensive analysis of this legal document, including its purpose, key terms, and important provisions.",
            "risks": "Identify potential legal risks and concerns in this document.",
            "summary": "Provide a concise summary of this legal document's main points.",
            "compliance": "Analyze this document for compliance issues and regulatory considerations."
        }
        
        prompt = f"""You are a legal AI assistant. Please analyze the following legal document.

DOCUMENT:
{document_text[:2000]}  # Limit document length

ANALYSIS REQUEST: {analysis_prompts.get(analysis_type, analysis_prompts["general"])}

ANALYSIS:"""
        
        try:
            response = self.pipeline(
                prompt,
                max_length=config.model.llm_max_length,
                temperature=0.1,  # Lower temperature for analysis
                top_p=config.model.llm_top_p,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            return self._clean_response(response[0]["generated_text"])
            
        except Exception as e:
            logger.error(f"Error analyzing document: {e}")
            return "Error occurred during document analysis."
    
    def extract_key_terms(self, document_text: str) -> List[str]:
        """
        Extract key legal terms from a document.
        
        Args:
            document_text: Text of the legal document
        
        Returns:
            List of key terms
        """
        prompt = f"""Extract the key legal terms and concepts from this document. Return only a comma-separated list of terms.

DOCUMENT:
{document_text[:1500]}

KEY TERMS:"""
        
        try:
            response = self.pipeline(
                prompt,
                max_length=200,
                temperature=0.1,
                do_sample=False,
                pad_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            terms_text = self._clean_response(response[0]["generated_text"])
            terms = [term.strip() for term in terms_text.split(",") if term.strip()]
            
            return terms[:10]  # Limit to top 10 terms
            
        except Exception as e:
            logger.error(f"Error extracting key terms: {e}")
            return []
    
    def cleanup(self):
        """Clean up model resources."""
        try:
            if self.model is not None:
                del self.model
            if self.tokenizer is not None:
                del self.tokenizer
            if self.pipeline is not None:
                del self.pipeline
            
            # Clear GPU cache if using CUDA
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # Force garbage collection
            gc.collect()
            
            logger.info("Model resources cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": config.model.llm_model_name,
            "device": self.device,
            "max_length": config.model.llm_max_length,
            "temperature": config.model.llm_temperature,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None
        }
