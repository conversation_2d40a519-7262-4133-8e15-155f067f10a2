"""
Document processing module for the Legal RAG System.
Handles PDF extraction, text processing, and intelligent chunking.
"""

import os
import fitz  # PyMuPDF
import pdfplumber
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import hashlib
from loguru import logger
from tqdm import tqdm

from config import config
from utils import (
    get_file_hash, extract_legal_document_type, clean_legal_text,
    chunk_legal_document, save_cache, load_cache
)


class DocumentProcessor:
    """Handles processing of legal documents from various formats."""
    
    def __init__(self):
        self.supported_extensions = config.document.supported_extensions
        self.cache_dir = Path(config.document.processed_docs_cache)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("DocumentProcessor initialized")
    
    def process_documents_folder(self, folder_path: str = None) -> List[Dict[str, Any]]:
        """
        Process all documents in the specified folder.
        
        Args:
            folder_path: Path to folder containing documents
        
        Returns:
            List of processed document chunks with metadata
        """
        if folder_path is None:
            folder_path = config.document.documents_folder
        
        folder_path = Path(folder_path)
        if not folder_path.exists():
            raise ValueError(f"Documents folder does not exist: {folder_path}")
        
        logger.info(f"Processing documents from: {folder_path}")
        
        # Get all supported files
        files = []
        for ext in self.supported_extensions:
            files.extend(folder_path.glob(f"*{ext}"))
        
        if not files:
            logger.warning(f"No supported documents found in {folder_path}")
            return []
        
        logger.info(f"Found {len(files)} documents to process")
        
        all_chunks = []
        for file_path in tqdm(files, desc="Processing documents"):
            try:
                chunks = self.process_document(file_path)
                all_chunks.extend(chunks)
                logger.info(f"Processed {file_path.name}: {len(chunks)} chunks")
            except Exception as e:
                logger.error(f"Error processing {file_path.name}: {e}")
                continue
        
        logger.info(f"Total chunks processed: {len(all_chunks)}")
        return all_chunks
    
    def process_document(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        Process a single document file.
        
        Args:
            file_path: Path to the document file
        
        Returns:
            List of document chunks with metadata
        """
        file_path = Path(file_path)
        
        # Check cache first
        file_hash = get_file_hash(str(file_path))
        cache_file = self.cache_dir / f"{file_path.stem}_{file_hash}.json"
        
        cached_data = load_cache(str(cache_file))
        if cached_data:
            logger.debug(f"Using cached data for {file_path.name}")
            return cached_data
        
        # Extract text based on file type
        if file_path.suffix.lower() == '.pdf':
            text, metadata = self._extract_pdf_text(file_path)
        elif file_path.suffix.lower() == '.docx':
            text, metadata = self._extract_docx_text(file_path)
        elif file_path.suffix.lower() == '.txt':
            text, metadata = self._extract_txt_text(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
        
        if not text.strip():
            logger.warning(f"No text extracted from {file_path.name}")
            return []
        
        # Clean the text
        cleaned_text = clean_legal_text(text)
        
        # Determine document type
        doc_type = extract_legal_document_type(file_path.name, cleaned_text)
        
        # Create chunks
        chunks = chunk_legal_document(
            cleaned_text,
            preserve_structure=config.document.preserve_legal_structure
        )
        
        # Add metadata to chunks
        processed_chunks = []
        for i, chunk in enumerate(chunks):
            # Convert citations list to string for ChromaDB compatibility
            citations = chunk.get("citations", [])
            citations_str = "; ".join(citations) if citations else ""

            # Clean metadata to ensure ChromaDB compatibility
            clean_metadata = {}
            for key, value in metadata.items():
                if isinstance(value, (list, dict)):
                    # Convert complex types to strings
                    clean_metadata[key] = str(value)
                elif value is None:
                    # Skip None values
                    continue
                else:
                    clean_metadata[key] = value

            chunk_data = {
                "id": f"{file_path.stem}_{i}",
                "text": chunk["text"],
                "metadata": {
                    "document_name": file_path.name,
                    "document_path": str(file_path),
                    "document_type": doc_type,
                    "chunk_index": i,
                    "chunk_size": chunk["size"],
                    "section_type": chunk.get("section_type", "content"),
                    "citations": citations_str,
                    "file_hash": file_hash,
                    "processed_at": datetime.now().isoformat(),
                    **clean_metadata
                }
            }
            processed_chunks.append(chunk_data)
        
        # Cache the results
        save_cache(processed_chunks, str(cache_file))
        
        return processed_chunks
    
    def _extract_pdf_text(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text from PDF file using PyMuPDF and pdfplumber.
        
        Args:
            file_path: Path to PDF file
        
        Returns:
            Tuple of (extracted_text, metadata)
        """
        text_parts = []
        metadata = {
            "total_pages": 0,
            "extraction_method": "pymupdf_pdfplumber",
            "has_images": False,
            "creation_date": None,
            "author": None,
            "title": None
        }
        
        try:
            # First try with PyMuPDF for better text extraction
            doc = fitz.open(str(file_path))
            metadata["total_pages"] = len(doc)
            
            # Extract document metadata
            doc_metadata = doc.metadata
            metadata.update({
                "creation_date": doc_metadata.get("creationDate"),
                "author": doc_metadata.get("author"),
                "title": doc_metadata.get("title")
            })
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Check for images
                if page.get_images():
                    metadata["has_images"] = True
                
                # Extract text
                page_text = page.get_text()
                
                if page_text.strip():
                    # Add page marker for reference
                    text_parts.append(f"\n[PAGE {page_num + 1}]\n{page_text}")
                else:
                    # If PyMuPDF fails, try pdfplumber for this page
                    try:
                        with pdfplumber.open(str(file_path)) as pdf:
                            if page_num < len(pdf.pages):
                                plumber_text = pdf.pages[page_num].extract_text()
                                if plumber_text:
                                    text_parts.append(f"\n[PAGE {page_num + 1}]\n{plumber_text}")
                    except Exception as e:
                        logger.warning(f"pdfplumber failed for page {page_num + 1}: {e}")
            
            doc.close()
            
        except Exception as e:
            logger.error(f"Error extracting PDF text from {file_path}: {e}")
            # Fallback to pdfplumber only
            try:
                with pdfplumber.open(str(file_path)) as pdf:
                    metadata["total_pages"] = len(pdf.pages)
                    metadata["extraction_method"] = "pdfplumber_fallback"
                    
                    for page_num, page in enumerate(pdf.pages):
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(f"\n[PAGE {page_num + 1}]\n{page_text}")
            except Exception as e2:
                logger.error(f"Fallback extraction also failed: {e2}")
                raise e2
        
        full_text = "\n".join(text_parts)
        return full_text, metadata
    
    def _extract_docx_text(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text from DOCX file.
        
        Args:
            file_path: Path to DOCX file
        
        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            from docx import Document
            
            doc = Document(str(file_path))
            
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            metadata = {
                "total_paragraphs": len(doc.paragraphs),
                "extraction_method": "python-docx"
            }
            
            return "\n\n".join(text_parts), metadata
            
        except ImportError:
            logger.error("python-docx not installed. Cannot process DOCX files.")
            raise
        except Exception as e:
            logger.error(f"Error extracting DOCX text from {file_path}: {e}")
            raise
    
    def _extract_txt_text(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text from TXT file.
        
        Args:
            file_path: Path to TXT file
        
        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            metadata = {
                "file_size": file_path.stat().st_size,
                "extraction_method": "direct_read"
            }
            
            return text, metadata
            
        except UnicodeDecodeError:
            # Try different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        text = f.read()
                    
                    metadata = {
                        "file_size": file_path.stat().st_size,
                        "extraction_method": "direct_read",
                        "encoding": encoding
                    }
                    
                    return text, metadata
                except UnicodeDecodeError:
                    continue
            
            raise ValueError(f"Could not decode text file: {file_path}")
        
        except Exception as e:
            logger.error(f"Error extracting TXT text from {file_path}: {e}")
            raise
    
    def get_document_stats(self, folder_path: str = None) -> Dict[str, Any]:
        """
        Get statistics about documents in the folder.
        
        Args:
            folder_path: Path to documents folder
        
        Returns:
            Dictionary with document statistics
        """
        if folder_path is None:
            folder_path = config.document.documents_folder
        
        folder_path = Path(folder_path)
        
        stats = {
            "total_files": 0,
            "file_types": {},
            "total_size_mb": 0,
            "files": []
        }
        
        for ext in self.supported_extensions:
            files = list(folder_path.glob(f"*{ext}"))
            stats["file_types"][ext] = len(files)
            stats["total_files"] += len(files)
            
            for file_path in files:
                file_size = file_path.stat().st_size
                stats["total_size_mb"] += file_size / (1024 * 1024)
                stats["files"].append({
                    "name": file_path.name,
                    "size_mb": file_size / (1024 * 1024),
                    "type": ext
                })
        
        return stats
