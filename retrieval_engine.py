"""
Retrieval engine for the Legal RAG System.
Handles intelligent document retrieval with legal domain expertise.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
from collections import defaultdict

from config import config
from vector_store import VectorStore
from utils import extract_legal_citations, validate_query


class LegalRetrievalEngine:
    """Advanced retrieval engine with legal domain expertise."""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.citation_patterns = self._compile_citation_patterns()
        
        logger.info("LegalRetrievalEngine initialized")
    
    def _compile_citation_patterns(self) -> List[re.Pattern]:
        """Compile regex patterns for legal citations."""
        patterns = [
            r'\d+\s+U\.S\.C\.\s+§\s+\d+',  # USC citations
            r'\d+\s+F\.\d+\s+\d+',  # Federal reporter
            r'\d+\s+S\.Ct\.\s+\d+',  # Supreme Court
            r'Section\s+\d+(\.\d+)*',  # Section references
            r'Article\s+[IVX]+',  # Article references
            r'Exhibit\s+[A-Z]',  # Exhibit references
            r'Schedule\s+[A-Z]',  # Schedule references
        ]
        
        return [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def retrieve(
        self,
        query: str,
        top_k: int = None,
        use_hybrid_search: bool = None,
        filter_by_document_type: Optional[str] = None,
        include_citations: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query with legal domain enhancements.
        
        Args:
            query: Search query
            top_k: Number of results to return
            use_hybrid_search: Whether to use hybrid search
            filter_by_document_type: Filter by specific document type
            include_citations: Whether to include citation analysis
        
        Returns:
            List of retrieved documents with enhanced metadata
        """
        # Validate query
        is_valid, error_msg = validate_query(query)
        if not is_valid:
            logger.error(f"Invalid query: {error_msg}")
            return []
        
        if top_k is None:
            top_k = config.retrieval.top_k
        if use_hybrid_search is None:
            use_hybrid_search = config.vector_store.enable_hybrid_search
        
        logger.info(f"Retrieving documents for query: {query[:100]}...")
        
        # Prepare metadata filter
        metadata_filter = {}
        if filter_by_document_type:
            metadata_filter["document_type"] = filter_by_document_type
        
        # Enhance query with legal context
        enhanced_query = self._enhance_legal_query(query)
        
        # Perform search
        if use_hybrid_search:
            results = self.vector_store.hybrid_search(
                enhanced_query,
                top_k=top_k * 2,  # Get more results for reranking
                filter_metadata=metadata_filter if metadata_filter else None
            )
        else:
            results = self.vector_store.search(
                enhanced_query,
                top_k=top_k * 2,
                filter_metadata=metadata_filter if metadata_filter else None
            )
        
        # Apply legal domain reranking
        reranked_results = self._legal_rerank(query, results)
        
        # Add citation analysis if requested
        if include_citations:
            reranked_results = self._add_citation_analysis(query, reranked_results)
        
        # Apply final filtering and scoring
        final_results = self._apply_legal_filters(query, reranked_results)
        
        logger.info(f"Retrieved {len(final_results)} relevant documents")
        return final_results[:top_k]
    
    def _enhance_legal_query(self, query: str) -> str:
        """
        Enhance the query with legal domain context.
        
        Args:
            query: Original query
        
        Returns:
            Enhanced query
        """
        # Extract legal terms and concepts
        legal_terms = self._extract_legal_concepts(query)
        
        # Add synonyms and related terms
        enhanced_terms = []
        for term in legal_terms:
            enhanced_terms.extend(self._get_legal_synonyms(term))
        
        # Combine original query with enhanced terms
        if enhanced_terms:
            enhanced_query = f"{query} {' '.join(enhanced_terms)}"
        else:
            enhanced_query = query
        
        return enhanced_query
    
    def _extract_legal_concepts(self, text: str) -> List[str]:
        """Extract legal concepts from text."""
        legal_keywords = [
            "contract", "agreement", "liability", "indemnification",
            "warranty", "representation", "covenant", "breach",
            "termination", "assignment", "intellectual property",
            "confidentiality", "non-disclosure", "governing law",
            "jurisdiction", "arbitration", "damages", "remedy"
        ]
        
        text_lower = text.lower()
        found_concepts = []
        
        for keyword in legal_keywords:
            if keyword in text_lower:
                found_concepts.append(keyword)
        
        return found_concepts
    
    def _get_legal_synonyms(self, term: str) -> List[str]:
        """Get legal synonyms for a term."""
        synonym_map = {
            "contract": ["agreement", "accord"],
            "liability": ["responsibility", "obligation"],
            "breach": ["violation", "default"],
            "termination": ["expiration", "dissolution"],
            "assignment": ["transfer", "conveyance"],
            "damages": ["compensation", "remedy"],
            "warranty": ["guarantee", "assurance"],
            "indemnification": ["hold harmless", "protection"]
        }
        
        return synonym_map.get(term.lower(), [])
    
    def _legal_rerank(
        self,
        query: str,
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Rerank results using legal domain expertise.
        
        Args:
            query: Original query
            results: Initial search results
        
        Returns:
            Reranked results
        """
        query_citations = extract_legal_citations(query)
        query_concepts = self._extract_legal_concepts(query)
        
        for result in results:
            original_score = result["score"]
            boost_factor = 1.0
            
            # Boost for citation matches
            if query_citations:
                doc_citations = result["metadata"].get("citations", [])
                citation_overlap = len(set(query_citations) & set(doc_citations))
                if citation_overlap > 0:
                    boost_factor *= (1 + citation_overlap * config.retrieval.cross_reference_boost)
            
            # Boost for legal concept matches
            doc_text = result["text"].lower()
            concept_matches = sum(1 for concept in query_concepts if concept in doc_text)
            if concept_matches > 0:
                boost_factor *= (1 + concept_matches * 0.1)
            
            # Boost for document type relevance
            doc_type = result["metadata"].get("document_type", "")
            if self._is_relevant_document_type(query, doc_type):
                boost_factor *= 1.2
            
            # Boost for section type relevance
            section_type = result["metadata"].get("section_type", "content")
            if self._is_relevant_section_type(query, section_type):
                boost_factor *= 1.1
            
            # Apply boost
            result["score"] = original_score * boost_factor
            result["legal_relevance_boost"] = boost_factor
        
        # Sort by new scores
        results.sort(key=lambda x: x["score"], reverse=True)
        return results
    
    def _is_relevant_document_type(self, query: str, doc_type: str) -> bool:
        """Check if document type is relevant to query."""
        query_lower = query.lower()
        doc_type_lower = doc_type.lower()
        
        relevance_map = {
            "assignment": ["assign", "transfer", "convey"],
            "contract": ["contract", "agreement", "deal"],
            "release": ["release", "waive", "discharge"],
            "consent": ["consent", "approve", "authorize"],
            "dissolution": ["dissolve", "terminate", "wind up"],
            "intellectual_property": ["ip", "patent", "trademark", "copyright"]
        }
        
        if doc_type_lower in relevance_map:
            return any(keyword in query_lower for keyword in relevance_map[doc_type_lower])
        
        return False
    
    def _is_relevant_section_type(self, query: str, section_type: str) -> bool:
        """Check if section type is relevant to query."""
        query_lower = query.lower()
        
        section_relevance = {
            "definitions": ["define", "definition", "meaning"],
            "representations": ["represent", "warranty", "assure"],
            "covenants": ["covenant", "promise", "undertake"],
            "indemnification": ["indemnify", "hold harmless", "protect"],
            "termination": ["terminate", "end", "expire"]
        }
        
        for section, keywords in section_relevance.items():
            if section in section_type.lower():
                return any(keyword in query_lower for keyword in keywords)
        
        return False
    
    def _add_citation_analysis(
        self,
        query: str,
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Add citation analysis to results."""
        query_citations = extract_legal_citations(query)
        
        for result in results:
            doc_citations = result["metadata"].get("citations", [])
            
            # Find citation overlaps
            citation_overlap = list(set(query_citations) & set(doc_citations))
            
            # Find cross-references within the document collection
            cross_refs = self._find_cross_references(result, results)
            
            result["citation_analysis"] = {
                "document_citations": doc_citations,
                "query_citation_overlap": citation_overlap,
                "cross_references": cross_refs,
                "citation_relevance_score": len(citation_overlap) / max(len(query_citations), 1)
            }
        
        return results
    
    def _find_cross_references(
        self,
        target_result: Dict[str, Any],
        all_results: List[Dict[str, Any]]
    ) -> List[str]:
        """Find cross-references to other documents."""
        target_citations = target_result["metadata"].get("citations", [])
        cross_refs = []
        
        for result in all_results:
            if result["id"] == target_result["id"]:
                continue
            
            result_citations = result["metadata"].get("citations", [])
            overlap = set(target_citations) & set(result_citations)
            
            if overlap:
                doc_name = result["metadata"].get("document_name", "Unknown")
                cross_refs.append(f"{doc_name} (shared citations: {', '.join(overlap)})")
        
        return cross_refs
    
    def _apply_legal_filters(
        self,
        query: str,
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply final legal domain filters."""
        filtered_results = []
        
        for result in results:
            # Apply similarity threshold
            if result["score"] < config.retrieval.similarity_threshold:
                continue
            
            # Check for minimum content quality
            text_length = len(result["text"])
            if text_length < config.document.min_chunk_size:
                continue
            
            # Add legal relevance metadata
            result["legal_metadata"] = {
                "content_length": text_length,
                "legal_concepts": self._extract_legal_concepts(result["text"]),
                "citation_count": len(result["metadata"].get("citations", [])),
                "section_importance": self._calculate_section_importance(result)
            }
            
            filtered_results.append(result)
        
        return filtered_results
    
    def _calculate_section_importance(self, result: Dict[str, Any]) -> float:
        """Calculate the importance of a document section."""
        section_type = result["metadata"].get("section_type", "content")
        
        importance_weights = {
            "definitions": 0.9,
            "representations": 0.8,
            "warranties": 0.8,
            "covenants": 0.7,
            "indemnification": 0.8,
            "termination": 0.7,
            "governing_law": 0.6,
            "miscellaneous": 0.3,
            "content": 0.5
        }
        
        return importance_weights.get(section_type.lower(), 0.5)
    
    def get_related_documents(
        self,
        document_id: str,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find documents related to a specific document.
        
        Args:
            document_id: ID of the source document
            top_k: Number of related documents to return
        
        Returns:
            List of related documents
        """
        try:
            # Get the source document
            source_results = self.vector_store.collection.get(
                ids=[document_id],
                include=["documents", "metadatas"]
            )
            
            if not source_results["documents"]:
                return []
            
            source_text = source_results["documents"][0]
            source_metadata = source_results["metadatas"][0]
            
            # Use the document text as query to find similar documents
            related_results = self.vector_store.search(
                source_text[:500],  # Use first 500 chars as query
                top_k=top_k + 1,  # +1 to exclude self
                filter_metadata=None
            )
            
            # Remove the source document from results
            related_results = [r for r in related_results if r["id"] != document_id]
            
            return related_results[:top_k]
            
        except Exception as e:
            logger.error(f"Error finding related documents: {e}")
            return []
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get retrieval engine statistics."""
        return {
            "vector_store_stats": self.vector_store.get_collection_stats(),
            "retrieval_config": {
                "top_k": config.retrieval.top_k,
                "similarity_threshold": config.retrieval.similarity_threshold,
                "hybrid_search_enabled": config.vector_store.enable_hybrid_search,
                "semantic_weight": config.retrieval.semantic_weight,
                "keyword_weight": config.retrieval.keyword_weight
            }
        }
